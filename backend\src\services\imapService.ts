import { ImapFlow } from 'imapflow';
import { simpleParser } from 'mailparser';

interface ImapConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

interface EmailMessage {
  id: string;
  subject: string;
  from: string;
  to: string[];
  date: Date;
  body: string;
  attachments?: any[];
  flags: string[];
}

class ImapService {
  private client: ImapFlow | null = null;

  async connect(config: ImapConfig): Promise<void> {
    try {
      this.client = new ImapFlow({
        host: config.host,
        port: config.port,
        secure: config.secure,
        auth: config.auth,
        logger: false
      });

      await this.client.connect();
    } catch (error) {
      console.error('IMAP connection failed:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.logout();
      this.client = null;
    }
  }

  async getEmails(mailbox: string = 'INBOX', limit: number = 50): Promise<EmailMessage[]> {
    if (!this.client) {
      throw new Error('IMAP client not connected');
    }

    try {
      const lock = await this.client.getMailboxLock(mailbox);
      
      try {
        const messages = this.client.fetch('1:*', {
          envelope: true,
          bodyStructure: true,
          source: true,
          flags: true
        }, { limit });

        const emails: EmailMessage[] = [];

        for await (const message of messages) {
          try {
            const parsed = await simpleParser(message.source);
            
            const email: EmailMessage = {
              id: message.uid?.toString() || message.seq.toString(),
              subject: parsed.subject || 'No Subject',
              from: parsed.from?.text || 'Unknown',
              to: parsed.to?.text ? [parsed.to.text] : [],
              date: parsed.date || new Date(),
              body: parsed.text || parsed.html || '',
              attachments: parsed.attachments,
              flags: message.flags || []
            };

            emails.push(email);
          } catch (parseError) {
            console.error('Error parsing email:', parseError);
          }
        }

        return emails;
      } finally {
        lock.release();
      }
    } catch (error) {
      console.error('Error fetching emails:', error);
      throw error;
    }
  }

  async markAsRead(messageId: string): Promise<void> {
    if (!this.client) {
      throw new Error('IMAP client not connected');
    }

    try {
      await this.client.messageFlagsAdd(messageId, ['\\Seen']);
    } catch (error) {
      console.error('Error marking email as read:', error);
      throw error;
    }
  }

  async deleteEmail(messageId: string): Promise<void> {
    if (!this.client) {
      throw new Error('IMAP client not connected');
    }

    try {
      await this.client.messageFlagsAdd(messageId, ['\\Deleted']);
      await this.client.expunge();
    } catch (error) {
      console.error('Error deleting email:', error);
      throw error;
    }
  }
}

export default new ImapService();
