"use strict";
/**
 * 真实邮件服务
 * 集成 Postfix + Dovecot + MySQL
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RealMailService = void 0;
const nodemailer_1 = __importDefault(require("nodemailer"));
const promise_1 = __importDefault(require("mysql2/promise"));
const imapflow_1 = require("imapflow");
const mailparser_1 = require("mailparser");
const logger_1 = require("../utils/logger");
class RealMailService {
    constructor(config) {
        this.config = config;
        this.initializeServices();
    }
    async initializeServices() {
        try {
            // 初始化 SMTP 传输器
            this.smtpTransporter = nodemailer_1.default.createTransport({
                host: this.config.smtp.host,
                port: this.config.smtp.port,
                secure: this.config.smtp.secure,
                auth: this.config.smtp.auth,
                pool: true,
                maxConnections: 5,
                maxMessages: 100,
            });
            // 验证 SMTP 连接
            await this.smtpTransporter.verify();
            logger_1.logger.info('SMTP 连接验证成功');
            // 初始化数据库连接
            this.dbConnection = await promise_1.default.createConnection(this.config.database);
            logger_1.logger.info('数据库连接成功');
        }
        catch (error) {
            logger_1.logger.error('邮件服务初始化失败:', error);
            throw error;
        }
    }
    /**
     * 发送邮件
     */
    async sendEmail(email, userEmail) {
        try {
            const mailOptions = {
                from: `${email.from.name || ''} <${email.from.address}>`,
                to: email.to.map(addr => `${addr.name || ''} <${addr.address}>`).join(', '),
                cc: email.cc?.map(addr => `${addr.name || ''} <${addr.address}>`).join(', '),
                bcc: email.bcc?.map(addr => `${addr.name || ''} <${addr.address}>`).join(', '),
                replyTo: email.replyTo,
                subject: email.subject,
                text: email.textContent,
                html: email.htmlContent,
                attachments: email.attachments?.map(att => ({
                    filename: att.filename,
                    content: att.content,
                    contentType: att.contentType,
                })),
                priority: email.priority || 'normal',
                headers: email.headers,
            };
            const result = await this.smtpTransporter.sendMail(mailOptions);
            // 记录发送日志
            await this.logEmailSend(userEmail, email, 'sent', result.messageId);
            // 保存到已发送文件夹
            await this.saveEmailToFolder(email, userEmail, 'sent');
            logger_1.logger.info(`邮件发送成功: ${result.messageId}`);
            return result.messageId;
        }
        catch (error) {
            logger_1.logger.error('邮件发送失败:', error);
            await this.logEmailSend(userEmail, email, 'failed', undefined, error.message);
            throw error;
        }
    }
    /**
     * 获取邮件列表
     */
    async getEmails(userEmail, folderId, options = {}) {
        try {
            const { page = 1, limit = 20, search, unreadOnly } = options;
            const offset = (page - 1) * limit;
            let query = `
        SELECT 
          e.id, e.message_id, e.thread_id, e.subject,
          e.from_address, e.from_name, e.to_addresses,
          e.cc_addresses, e.bcc_addresses, e.reply_to,
          e.date_sent, e.date_received, e.size,
          e.has_attachments, e.attachment_count,
          e.priority, e.body_text, e.body_html,
          ue.is_read, ue.is_starred, ue.is_flagged,
          ue.read_at, ue.labels
        FROM emails e
        JOIN user_emails ue ON e.id = ue.email_id
        JOIN mail_folders mf ON ue.folder_id = mf.id
        JOIN users u ON mf.user_id = u.id
        WHERE u.email = ? AND mf.id = ? AND ue.is_deleted = 0
      `;
            const params = [userEmail, folderId];
            if (unreadOnly) {
                query += ' AND ue.is_read = 0';
            }
            if (search) {
                query += ' AND (e.subject LIKE ? OR e.from_address LIKE ? OR e.body_text LIKE ?)';
                const searchPattern = `%${search}%`;
                params.push(searchPattern, searchPattern, searchPattern);
            }
            query += ' ORDER BY e.date_received DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);
            const [rows] = await this.dbConnection.execute(query, params);
            const emails = rows.map(row => this.mapRowToEmail(row));
            // 获取总数
            let countQuery = `
        SELECT COUNT(*) as total
        FROM emails e
        JOIN user_emails ue ON e.id = ue.email_id
        JOIN mail_folders mf ON ue.folder_id = mf.id
        JOIN users u ON mf.user_id = u.id
        WHERE u.email = ? AND mf.id = ? AND ue.is_deleted = 0
      `;
            const countParams = [userEmail, folderId];
            if (unreadOnly) {
                countQuery += ' AND ue.is_read = 0';
            }
            if (search) {
                countQuery += ' AND (e.subject LIKE ? OR e.from_address LIKE ? OR e.body_text LIKE ?)';
                const searchPattern = `%${search}%`;
                countParams.push(searchPattern, searchPattern, searchPattern);
            }
            const [countRows] = await this.dbConnection.execute(countQuery, countParams);
            const total = countRows[0].total;
            return {
                emails,
                total,
                hasMore: offset + emails.length < total,
            };
        }
        catch (error) {
            logger_1.logger.error('获取邮件列表失败:', error);
            throw error;
        }
    }
    /**
     * 获取单个邮件详情
     */
    async getEmail(userEmail, emailId) {
        try {
            const query = `
        SELECT 
          e.id, e.message_id, e.thread_id, e.subject,
          e.from_address, e.from_name, e.to_addresses,
          e.cc_addresses, e.bcc_addresses, e.reply_to,
          e.date_sent, e.date_received, e.size,
          e.has_attachments, e.attachment_count,
          e.priority, e.body_text, e.body_html,
          e.raw_headers, ue.is_read, ue.is_starred,
          ue.is_flagged, ue.read_at, ue.labels
        FROM emails e
        JOIN user_emails ue ON e.id = ue.email_id
        JOIN mail_folders mf ON ue.folder_id = mf.id
        JOIN users u ON mf.user_id = u.id
        WHERE u.email = ? AND e.id = ? AND ue.is_deleted = 0
      `;
            const [rows] = await this.dbConnection.execute(query, [userEmail, emailId]);
            if (rows.length === 0) {
                return null;
            }
            const email = this.mapRowToEmail(rows[0]);
            // 获取附件
            if (email.attachments && email.attachments.length > 0) {
                email.attachments = await this.getEmailAttachments(emailId);
            }
            // 标记为已读
            if (!rows[0].is_read) {
                await this.markEmailAsRead(userEmail, emailId);
            }
            return email;
        }
        catch (error) {
            logger_1.logger.error('获取邮件详情失败:', error);
            throw error;
        }
    }
    /**
     * 获取文件夹列表
     */
    async getFolders(userEmail) {
        try {
            const query = `
        SELECT 
          mf.id, mf.name, mf.display_name, mf.type,
          mf.email_count, mf.unread_count, mf.sort_order
        FROM mail_folders mf
        JOIN users u ON mf.user_id = u.id
        WHERE u.email = ?
        ORDER BY mf.sort_order, mf.name
      `;
            const [rows] = await this.dbConnection.execute(query, [userEmail]);
            return rows.map(row => ({
                id: row.id.toString(),
                name: row.name,
                displayName: row.display_name,
                type: row.type,
                emailCount: row.email_count,
                unreadCount: row.unread_count,
            }));
        }
        catch (error) {
            logger_1.logger.error('获取文件夹列表失败:', error);
            throw error;
        }
    }
    /**
     * 同步 IMAP 邮件
     */
    async syncImapEmails(userEmail) {
        let client = null;
        try {
            // 创建 IMAP 连接
            client = new imapflow_1.ImapFlow({
                host: this.config.imap.host,
                port: this.config.imap.port,
                secure: this.config.imap.secure,
                auth: {
                    user: userEmail,
                    pass: this.config.imap.auth.pass, // 实际应用中应该从数据库获取用户密码
                },
                logger: false,
            });
            await client.connect();
            // 获取邮箱列表
            const mailboxes = await client.list();
            for (const mailbox of mailboxes) {
                if (mailbox.flags.includes('\\Noselect')) {
                    continue;
                }
                // 选择邮箱
                const lock = await client.getMailboxLock(mailbox.path);
                try {
                    // 获取邮件
                    const messages = client.fetch('1:*', {
                        envelope: true,
                        bodyStructure: true,
                        source: true,
                    });
                    for await (const message of messages) {
                        await this.processImapMessage(userEmail, message, mailbox.path);
                    }
                }
                finally {
                    lock.release();
                }
            }
            logger_1.logger.info(`IMAP 同步完成: ${userEmail}`);
        }
        catch (error) {
            logger_1.logger.error('IMAP 同步失败:', error);
            throw error;
        }
        finally {
            if (client) {
                await client.logout();
            }
        }
    }
    /**
     * 处理 IMAP 消息
     */
    async processImapMessage(userEmail, message, folderPath) {
        try {
            const parsed = await (0, mailparser_1.simpleParser)(message.source);
            const email = {
                messageId: parsed.messageId || `${Date.now()}-${Math.random()}`,
                threadId: parsed.inReplyTo || parsed.messageId,
                subject: parsed.subject || '(无主题)',
                from: {
                    address: parsed.from?.value[0]?.address || '',
                    name: parsed.from?.value[0]?.name,
                },
                to: parsed.to?.value.map((addr) => ({
                    address: addr.address || '',
                    name: addr.name,
                })) || [],
                cc: parsed.cc?.value.map((addr) => ({
                    address: addr.address || '',
                    name: addr.name,
                })),
                bcc: parsed.bcc?.value.map((addr) => ({
                    address: addr.address || '',
                    name: addr.name,
                })),
                replyTo: parsed.replyTo?.value[0]?.address,
                date: parsed.date || new Date(),
                textContent: parsed.text,
                htmlContent: parsed.html,
                attachments: parsed.attachments?.map((att) => ({
                    filename: att.filename || 'attachment',
                    contentType: att.contentType || 'application/octet-stream',
                    size: att.size || 0,
                    content: att.content,
                })),
                headers: parsed.headers,
                priority: this.getPriorityFromHeaders(parsed.headers),
            };
            // 保存邮件到数据库
            await this.saveEmailToDatabase(email, userEmail, folderPath);
        }
        catch (error) {
            logger_1.logger.error('处理 IMAP 消息失败:', error);
        }
    }
    /**
     * 辅助方法
     */
    mapRowToEmail(row) {
        return {
            id: row.id.toString(),
            messageId: row.message_id,
            threadId: row.thread_id,
            subject: row.subject,
            from: {
                address: row.from_address,
                name: row.from_name,
            },
            to: JSON.parse(row.to_addresses || '[]'),
            cc: JSON.parse(row.cc_addresses || '[]'),
            bcc: JSON.parse(row.bcc_addresses || '[]'),
            replyTo: row.reply_to,
            date: row.date_sent || row.date_received,
            textContent: row.body_text,
            htmlContent: row.body_html,
            attachments: row.has_attachments ? [] : undefined, // 需要单独查询
            priority: row.priority,
        };
    }
    async getEmailAttachments(emailId) {
        const query = `
      SELECT filename, original_filename, content_type, size, file_path, is_inline, content_id
      FROM email_attachments
      WHERE email_id = ?
    `;
        const [rows] = await this.dbConnection.execute(query, [emailId]);
        return rows;
    }
    async markEmailAsRead(userEmail, emailId) {
        const query = `
      UPDATE user_emails ue
      JOIN mail_folders mf ON ue.folder_id = mf.id
      JOIN users u ON mf.user_id = u.id
      SET ue.is_read = 1, ue.read_at = NOW()
      WHERE u.email = ? AND ue.email_id = ?
    `;
        await this.dbConnection.execute(query, [userEmail, emailId]);
    }
    async logEmailSend(userEmail, email, status, messageId, errorMessage) {
        const query = `
      INSERT INTO mail_send_logs (user_id, from_address, to_address, subject, status, error_message, sent_at)
      SELECT u.id, ?, ?, ?, ?, ?, ?
      FROM users u WHERE u.email = ?
    `;
        const toAddress = email.to[0]?.address || '';
        const sentAt = status === 'sent' ? new Date() : null;
        await this.dbConnection.execute(query, [
            email.from.address,
            toAddress,
            email.subject,
            status,
            errorMessage,
            sentAt,
            userEmail,
        ]);
    }
    async saveEmailToFolder(email, userEmail, folderType) {
        // 实现保存邮件到指定文件夹的逻辑
        // 这里需要根据具体需求实现
    }
    async saveEmailToDatabase(email, userEmail, folderPath) {
        // 实现保存邮件到数据库的逻辑
        // 这里需要根据具体需求实现
    }
    getPriorityFromHeaders(headers) {
        const priority = headers.get('x-priority') || headers.get('priority');
        if (priority) {
            const p = priority.toString().toLowerCase();
            if (p.includes('high') || p === '1')
                return 'high';
            if (p.includes('low') || p === '5')
                return 'low';
        }
        return 'normal';
    }
    /**
     * 关闭连接
     */
    async close() {
        try {
            if (this.smtpTransporter) {
                this.smtpTransporter.close();
            }
            if (this.dbConnection) {
                await this.dbConnection.end();
            }
            logger_1.logger.info('邮件服务连接已关闭');
        }
        catch (error) {
            logger_1.logger.error('关闭邮件服务连接失败:', error);
        }
    }
}
exports.RealMailService = RealMailService;
//# sourceMappingURL=realMailService.js.map