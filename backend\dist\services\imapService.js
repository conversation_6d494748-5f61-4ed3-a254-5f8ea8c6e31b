"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const imapflow_1 = require("imapflow");
const mailparser_1 = require("mailparser");
class ImapService {
    constructor() {
        this.client = null;
    }
    async connect(config) {
        try {
            this.client = new imapflow_1.ImapFlow({
                host: config.host,
                port: config.port,
                secure: config.secure,
                auth: config.auth,
                logger: false
            });
            await this.client.connect();
        }
        catch (error) {
            console.error('IMAP connection failed:', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.client) {
            await this.client.logout();
            this.client = null;
        }
    }
    async getEmails(mailbox = 'INBOX', limit = 50) {
        if (!this.client) {
            throw new Error('IMAP client not connected');
        }
        try {
            const lock = await this.client.getMailboxLock(mailbox);
            try {
                const messages = this.client.fetch('1:*', {
                    envelope: true,
                    bodyStructure: true,
                    source: true,
                    flags: true
                }, { limit });
                const emails = [];
                for await (const message of messages) {
                    try {
                        const parsed = await (0, mailparser_1.simpleParser)(message.source);
                        const email = {
                            id: message.uid?.toString() || message.seq.toString(),
                            subject: parsed.subject || 'No Subject',
                            from: parsed.from?.text || 'Unknown',
                            to: parsed.to?.text ? [parsed.to.text] : [],
                            date: parsed.date || new Date(),
                            body: parsed.text || parsed.html || '',
                            attachments: parsed.attachments,
                            flags: message.flags || []
                        };
                        emails.push(email);
                    }
                    catch (parseError) {
                        console.error('Error parsing email:', parseError);
                    }
                }
                return emails;
            }
            finally {
                lock.release();
            }
        }
        catch (error) {
            console.error('Error fetching emails:', error);
            throw error;
        }
    }
    async markAsRead(messageId) {
        if (!this.client) {
            throw new Error('IMAP client not connected');
        }
        try {
            await this.client.messageFlagsAdd(messageId, ['\\Seen']);
        }
        catch (error) {
            console.error('Error marking email as read:', error);
            throw error;
        }
    }
    async deleteEmail(messageId) {
        if (!this.client) {
            throw new Error('IMAP client not connected');
        }
        try {
            await this.client.messageFlagsAdd(messageId, ['\\Deleted']);
            await this.client.expunge();
        }
        catch (error) {
            console.error('Error deleting email:', error);
            throw error;
        }
    }
}
exports.default = new ImapService();
//# sourceMappingURL=imapService.js.map