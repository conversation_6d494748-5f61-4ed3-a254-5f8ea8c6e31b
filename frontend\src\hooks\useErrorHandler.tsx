/**
 * 错误处理Hook
 * 提供统一的错误处理功能
 */

import React from 'react';
import { message, notification } from 'antd';

// 错误类型常量
export const ErrorType = {
  VALIDATION: 'VALIDATION',
  AUTHENTICATION: 'AUTHENTICATION',
  AUTHORIZATION: 'AUTHORIZATION',
  NOT_FOUND: 'NOT_FOUND',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_ERROR: 'SERVER_ERROR',
  UNKNOWN: 'UNKNOWN',
} as const;

export type ErrorType = typeof ErrorType[keyof typeof ErrorType];

// 错误代码常量
export const ErrorCode = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVER_UNAVAILABLE: 'SERVER_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  VALIDATION_FAILED: 'VALIDATION_FAILED',
} as const;

export type ErrorCode = typeof ErrorCode[keyof typeof ErrorCode];

// 错误信息接口
export interface ErrorInfo {
  type?: ErrorType;
  code?: ErrorCode;
  message: string;
  details?: any;
  requestId?: string;
  timestamp?: string;
}

// 错误处理选项
export interface ErrorHandlerOptions {
  showNotification?: boolean;
  showMessage?: boolean;
  autoClose?: boolean;
  duration?: number;
  onError?: (error: ErrorInfo) => void;
}

// 默认错误处理选项
const defaultOptions: ErrorHandlerOptions = {
  showNotification: true,
  showMessage: false,
  autoClose: true,
  duration: 4.5,
};

// 错误处理Hook
export const useErrorHandler = (options: ErrorHandlerOptions = {}) => {
  const mergedOptions = { ...defaultOptions, ...options };

  const handleError = React.useCallback((error: any, customMessage?: string) => {
    const errorInfo = parseError(error, customMessage);
    
    // 调用自定义错误处理器
    if (mergedOptions.onError) {
      mergedOptions.onError(errorInfo);
    }

    // 显示通知
    if (mergedOptions.showNotification) {
      showErrorNotificationInternal(errorInfo, mergedOptions);
    }

    // 显示消息
    if (mergedOptions.showMessage) {
      showErrorMessageInternal(errorInfo);
    }

    // 记录错误日志
    logError(errorInfo);

    return errorInfo;
  }, [mergedOptions]);

  return { handleError };
};

// 解析错误对象
const parseError = (error: any, customMessage?: string): ErrorInfo => {
  // 如果已经是ErrorInfo格式
  if (error && typeof error === 'object' && error.message) {
    return {
      type: error.type || ErrorType.UNKNOWN,
      code: error.code,
      message: customMessage || error.message,
      details: error.details,
      requestId: error.requestId,
      timestamp: new Date().toISOString(),
    };
  }

  // 处理Axios错误
  if (error?.response) {
    const { status, data } = error.response;
    
    let errorType: ErrorType = ErrorType.UNKNOWN;
    let errorCode: ErrorCode | undefined;
    let message = customMessage || '请求失败';

    // 根据HTTP状态码确定错误类型
    switch (status) {
      case 400:
        errorType = ErrorType.VALIDATION;
        errorCode = ErrorCode.VALIDATION_FAILED;
        message = data?.message || '请求参数错误';
        break;
      case 401:
        errorType = ErrorType.AUTHENTICATION;
        errorCode = ErrorCode.INVALID_CREDENTIALS;
        message = data?.message || '身份验证失败';
        break;
      case 403:
        errorType = ErrorType.AUTHORIZATION;
        errorCode = ErrorCode.INSUFFICIENT_PERMISSIONS;
        message = data?.message || '权限不足';
        break;
      case 404:
        errorType = ErrorType.NOT_FOUND;
        errorCode = ErrorCode.RESOURCE_NOT_FOUND;
        message = data?.message || '资源不存在';
        break;
      case 429:
        errorType = ErrorType.SERVER_ERROR;
        errorCode = ErrorCode.RATE_LIMIT_EXCEEDED;
        message = data?.message || '请求过于频繁';
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        errorType = ErrorType.SERVER_ERROR;
        errorCode = ErrorCode.SERVER_UNAVAILABLE;
        message = data?.message || '服务器错误';
        break;
      default:
        message = data?.message || customMessage || `请求失败 (${status})`;
    }

    return {
      type: errorType,
      code: errorCode,
      message,
      details: data,
      requestId: data?.requestId,
      timestamp: new Date().toISOString(),
    };
  }

  // 处理网络错误
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return {
      type: ErrorType.NETWORK_ERROR,
      code: ErrorCode.NETWORK_ERROR,
      message: customMessage || '网络连接失败，请检查网络设置',
      timestamp: new Date().toISOString(),
    };
  }

  // 处理其他错误
  return {
    type: ErrorType.UNKNOWN,
    message: customMessage || error?.message || '未知错误',
    details: error,
    timestamp: new Date().toISOString(),
  };
};

// 显示错误通知（内部函数）
const showErrorNotificationInternal = (errorInfo: ErrorInfo, options: ErrorHandlerOptions) => {
  const { type: errorType, code: errorCode, message, requestId } = errorInfo;
  const notificationType = getNotificationType(errorType);

  notification[notificationType]({
    message: getErrorTitle(errorType, errorCode),
    description: (
      <div>
        <p>{message}</p>
        {requestId && (
          <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
            错误ID: {requestId}
          </p>
        )}
      </div>
    ),
    duration: options.autoClose ? options.duration : 0,
    placement: 'topRight',
  });
};

// 显示错误消息（内部函数）
const showErrorMessageInternal = (errorInfo: ErrorInfo) => {
  message.error(errorInfo.message);
};

// 获取通知类型
const getNotificationType = (errorType?: ErrorType): 'error' | 'warning' | 'info' => {
  switch (errorType) {
    case ErrorType.VALIDATION:
    case ErrorType.NOT_FOUND:
      return 'warning';
    case ErrorType.AUTHENTICATION:
    case ErrorType.AUTHORIZATION:
      return 'info';
    default:
      return 'error';
  }
};

// 获取错误标题
const getErrorTitle = (errorType?: ErrorType, errorCode?: ErrorCode): string => {
  if (errorCode) {
    switch (errorCode) {
      case ErrorCode.INVALID_CREDENTIALS:
        return '登录失败';
      case ErrorCode.TOKEN_EXPIRED:
        return '登录已过期';
      case ErrorCode.INSUFFICIENT_PERMISSIONS:
        return '权限不足';
      case ErrorCode.RESOURCE_NOT_FOUND:
        return '资源不存在';
      case ErrorCode.NETWORK_ERROR:
        return '网络错误';
      default:
        break;
    }
  }

  switch (errorType) {
    case ErrorType.VALIDATION:
      return '输入验证失败';
    case ErrorType.AUTHENTICATION:
      return '身份验证失败';
    case ErrorType.AUTHORIZATION:
      return '权限验证失败';
    case ErrorType.NOT_FOUND:
      return '资源不存在';
    case ErrorType.NETWORK_ERROR:
      return '网络连接错误';
    case ErrorType.SERVER_ERROR:
      return '服务器错误';
    default:
      return '操作失败';
  }
};

// 记录错误日志
const logError = (errorInfo: ErrorInfo) => {
  if (process.env.NODE_ENV === 'development') {
    console.error('Error occurred:', errorInfo);
  }
  
  // 在生产环境中，可以发送到日志服务
  // if (process.env.NODE_ENV === 'production') {
  //   sendToLogService(errorInfo);
  // }
};

// 预定义的错误处理器
export const useApiErrorHandler = () => {
  return useErrorHandler({
    showNotification: true,
    showMessage: false,
    autoClose: true,
    duration: 5,
  });
};

export const useFormErrorHandler = () => {
  return useErrorHandler({
    showNotification: false,
    showMessage: true,
    autoClose: true,
    duration: 3,
  });
};

export const useNetworkErrorHandler = () => {
  return useErrorHandler({
    showNotification: true,
    showMessage: false,
    autoClose: false, // 网络错误不自动关闭
  });
};

// 导出便捷函数
export const showErrorMessage = (error: any, customMessage?: string) => {
  const errorInfo = parseError(error, customMessage);
  message.error(errorInfo.message);
};

export const showErrorNotification = (error: any, customMessage?: string) => {
  const errorInfo = parseError(error, customMessage);
  const notificationType = getNotificationType(errorInfo.type);
  
  notification[notificationType]({
    message: getErrorTitle(errorInfo.type, errorInfo.code),
    description: errorInfo.message,
    duration: 4.5,
    placement: 'topRight',
  });
};
