{"version": 3, "file": "realEmailController.js", "sourceRoot": "", "sources": ["../../src/controllers/realEmailController.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAGH,qDAAsD;AACtD,4CAAyC;AACzC,oCAAiD;AACjD,6DAA0D;AAe1D;;GAEG;AACU,QAAA,SAAS,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACvF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EACJ,QAAQ,EACR,IAAI,GAAG,GAAG,EACV,KAAK,GAAG,IAAI,EACZ,MAAM,EACN,UAAU,EACX,GAAG,GAAG,CAAC,KAAK,CAAC;IAEd,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAE3C,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,QAAkB,EAAE;YAC7E,IAAI,EAAE,QAAQ,CAAC,IAAc,CAAC;YAC9B,KAAK,EAAE,QAAQ,CAAC,KAAe,CAAC;YAChC,MAAM,EAAE,MAAgB;YACxB,UAAU,EAAE,UAAU,KAAK,MAAM;SAClC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAA+B;YAC3C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,UAAU;SACpB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,YAAY,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IAC1F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAE1B,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAC3C,MAAM,KAAK,GAAG,MAAM,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAE7D,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACnC,CAAC;QAED,MAAM,QAAQ,GAA8B;YAC1C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,KAAK;YACX,OAAO,EAAE,UAAU;SACpB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjC,IAAI,KAAK,YAAY,gBAAQ,EAAE,CAAC;YAC9B,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,SAAS,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACvF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EACJ,EAAE,EACF,EAAE,EACF,GAAG,EACH,OAAO,EACP,WAAW,EACX,WAAW,EACX,WAAW,EACX,QAAQ,GAAG,QAAQ,GACpB,GAAG,GAAG,CAAC,IAAI,CAAC;IAEb,SAAS;IACT,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACjD,MAAM,IAAI,gBAAQ,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAE3C,MAAM,YAAY,GAAG;YACnB,SAAS,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3E,OAAO;YACP,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK;gBACvB,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,QAAQ;aACxB;YACD,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBACzB,OAAO,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACvD,IAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;aACvD,CAAC,CAAC;YACH,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC1B,OAAO,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACvD,IAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;aACvD,CAAC,CAAC;YACH,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;gBAC5B,OAAO,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;gBACvD,IAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;aACvD,CAAC,CAAC;YACH,IAAI,EAAE,IAAI,IAAI,EAAE;YAChB,WAAW;YACX,WAAW;YACX,WAAW;YACX,QAAQ;SACT,CAAC;QAEF,MAAM,SAAS,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE5E,MAAM,QAAQ,GAAuC;YACnD,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,SAAS,EAAE;YACnB,OAAO,EAAE,QAAQ;SAClB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/B,MAAM,IAAI,gBAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACpC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACzF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE5D,IAAI,CAAC;QACH,iBAAiB;QACjB,kBAAkB;QAClB,MAAM,QAAQ,GAAgC;YAC5C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,EAAE,EAAE;YACZ,OAAO,EAAE,UAAU;SACpB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,WAAW,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACzF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EAAE,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAE1B,IAAI,CAAC;QACH,eAAe;QACf,kBAAkB;QAClB,MAAM,QAAQ,GAAgC;YAC5C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,EAAE,EAAE;YACZ,OAAO,EAAE,QAAQ;SAClB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/B,MAAM,IAAI,gBAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACpC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,YAAY,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IAC1F,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,IAAI,GAAG,GAAG,EACV,KAAK,GAAG,IAAI,EACZ,QAAQ,EACR,MAAM,EACN,cAAc,GACf,GAAG,GAAG,CAAC,IAAI,CAAC;IAEb,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,gBAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAE3C,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,EAAE;YACnE,IAAI,EAAE,QAAQ,CAAC,IAAc,CAAC;YAC9B,KAAK,EAAE,QAAQ,CAAC,KAAe,CAAC;YAChC,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;QAEH,MAAM,QAAQ,GAA+B;YAC3C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,QAAQ;SAClB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QAC/B,MAAM,IAAI,gBAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IACpC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,UAAU,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACxF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7D,MAAM,QAAQ,GAAgC;YAC5C,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,WAAW;SACrB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QAClC,MAAM,IAAI,gBAAQ,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACvC,CAAC;AACH,CAAC,CAAC,CAAC;AAEH;;GAEG;AACU,QAAA,UAAU,GAAG,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAyB,EAAE,GAAa,EAAE,EAAE;IACxF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,gBAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAM,IAAA,2BAAc,GAAE,CAAC;QAE3C,eAAe;QACf,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;YAC9D,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAqC;YACjD,OAAO,EAAE,IAAI;YACb,IAAI,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE;YAC5B,OAAO,EAAE,eAAe;SACzB,CAAC;QAEF,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACrB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QACjC,MAAM,IAAI,gBAAQ,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC,CAAC"}