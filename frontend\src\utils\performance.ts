/**
 * 性能监控和优化工具
 * 提供性能测量、监控和优化的实用函数
 */

import { PERFORMANCE_CONFIG } from "../config/performance";

// 性能指标接口
export interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}
export type MemoryInfo = {
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
  heapSizeLimit: number;
};
// 性能监控器类
class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  // 初始化性能观察器
  private initializeObservers() {
    if (typeof window === "undefined" || !window.PerformanceObserver) {
      return;
    }

    try {
      // 观察导航性能
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.logNavigationMetrics(entry as PerformanceNavigationTiming);
        });
      });
      navObserver.observe({ entryTypes: ["navigation"] });
      this.observers.push(navObserver);

      // 观察资源加载性能
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.logResourceMetrics(entry as PerformanceResourceTiming);
        });
      });
      resourceObserver.observe({ entryTypes: ["resource"] });
      this.observers.push(resourceObserver);

      // 观察用户交互性能
      const measureObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.logMeasureMetrics(entry as PerformanceMeasure);
        });
      });
      measureObserver.observe({ entryTypes: ["measure"] });
      this.observers.push(measureObserver);
    } catch (error) {
      console.warn("Performance observers not supported:", error);
    }
  }

  // 开始性能测量
  startMeasure(name: string, metadata?: Record<string, any>): void {
    const startTime = performance.now();
    this.metrics.set(name, {
      name,
      startTime,
      metadata,
    });

    // 使用Performance API标记
    if (performance.mark) {
      performance.mark(`${name}-start`);
    }
  }

  // 结束性能测量
  endMeasure(name: string): PerformanceMetrics | null {
    const metric = this.metrics.get(name);
    if (!metric) {
      console.warn(`Performance measure "${name}" not found`);
      return null;
    }

    const endTime = performance.now();
    const duration = endTime - metric.startTime;

    const completedMetric: PerformanceMetrics = {
      ...metric,
      endTime,
      duration,
    };

    this.metrics.set(name, completedMetric);

    // 使用Performance API测量
    if (performance.mark && performance.measure) {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
    }

    // 记录性能数据
    this.logPerformanceMetric(completedMetric);

    return completedMetric;
  }

  // 获取性能指标
  getMetric(name: string): PerformanceMetrics | undefined {
    return this.metrics.get(name);
  }

  // 获取所有性能指标
  getAllMetrics(): PerformanceMetrics[] {
    return Array.from(this.metrics.values());
  }

  // 清除性能指标
  clearMetrics(): void {
    this.metrics.clear();
    if (performance.clearMarks) {
      performance.clearMarks();
    }
    if (performance.clearMeasures) {
      performance.clearMeasures();
    }
  }

  // 记录导航性能
  private logNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      dns: entry.domainLookupEnd - entry.domainLookupStart,
      tcp: entry.connectEnd - entry.connectStart,
      ssl:
        entry.secureConnectionStart > 0
          ? entry.connectEnd - entry.secureConnectionStart
          : 0,
      ttfb: entry.responseStart - entry.requestStart,
      download: entry.responseEnd - entry.responseStart,
      domParse: entry.domContentLoadedEventStart - entry.responseEnd,
      domReady:
        entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      total: entry.loadEventEnd - entry.loadEventStart,
    };

    if (PERFORMANCE_CONFIG.DEV.LOG_PERFORMANCE) {
      console.group("🚀 Navigation Performance");
      console.table(metrics);
      console.groupEnd();
    }

    // 检查性能预算
    this.checkPerformanceBudget(metrics);
  }

  // 记录资源性能
  private logResourceMetrics(entry: PerformanceResourceTiming): void {
    const duration = entry.responseEnd - entry.startTime;

    // 只记录较慢的资源
    if (duration > 100) {
      const resourceMetric = {
        name: entry.name,
        type: this.getResourceType(entry.name),
        duration,
        size: entry.transferSize || 0,
        cached: entry.transferSize === 0 && entry.decodedBodySize > 0,
      };

      if (PERFORMANCE_CONFIG.DEV.LOG_PERFORMANCE) {
        console.log("📦 Slow Resource:", resourceMetric);
      }
    }
  }

  // 记录测量性能
  private logMeasureMetrics(entry: PerformanceMeasure): void {
    if (PERFORMANCE_CONFIG.DEV.LOG_PERFORMANCE) {
      console.log(`⏱️ ${entry.name}: ${entry.duration.toFixed(2)}ms`);
    }

    // 检查渲染性能
    if (entry.duration > PERFORMANCE_CONFIG.MONITORING.RENDER_TIME_THRESHOLD) {
      console.warn(
        `🐌 Slow operation detected: ${
          entry.name
        } took ${entry.duration.toFixed(2)}ms`
      );
    }
  }

  // 记录性能指标
  private logPerformanceMetric(metric: PerformanceMetrics): void {
    if (!metric.duration) return;

    if (PERFORMANCE_CONFIG.DEV.LOG_PERFORMANCE) {
      console.log(
        `⏱️ ${metric.name}: ${metric.duration.toFixed(2)}ms`,
        metric.metadata
      );
    }

    // 发送到分析服务 (生产环境)
    if (
      process.env.NODE_ENV === "production" &&
      Math.random() < PERFORMANCE_CONFIG.MONITORING.SAMPLING_RATE
    ) {
      this.sendToAnalytics(metric);
    }
  }

  // 检查性能预算
  private checkPerformanceBudget(metrics: Record<string, number>): void {
    const budget = PERFORMANCE_CONFIG.MONITORING.PERFORMANCE_BUDGET;
    const warnings: string[] = [];

    if (metrics.total > budget.TTI) {
      warnings.push(`TTI exceeded: ${metrics.total}ms > ${budget.TTI}ms`);
    }

    if (warnings.length > 0) {
      console.warn("⚠️ Performance Budget Exceeded:", warnings);
    }
  }

  // 获取资源类型
  private getResourceType(url: string): string {
    if (url.includes(".js")) return "script";
    if (url.includes(".css")) return "stylesheet";
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return "image";
    if (url.match(/\.(woff|woff2|ttf|eot)$/)) return "font";
    return "other";
  }

  // 发送到分析服务
  private sendToAnalytics(metric: PerformanceMetrics): void {
    // 这里可以集成Google Analytics、Sentry等服务
    // navigator.sendBeacon('/api/analytics/performance', JSON.stringify(metric));
  }

  // 清理观察器
  destroy(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
    this.clearMetrics();
  }
}

// 创建全局性能监控器实例
export const performanceMonitor = new PerformanceMonitor();

// 性能装饰器
export function measurePerformance(name?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const measureName = name || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = function (...args: any[]) {
      performanceMonitor.startMeasure(measureName);

      try {
        const result = originalMethod.apply(this, args);

        // 处理Promise
        if (result && typeof result.then === "function") {
          return result.finally(() => {
            performanceMonitor.endMeasure(measureName);
          });
        }

        performanceMonitor.endMeasure(measureName);
        return result;
      } catch (error) {
        performanceMonitor.endMeasure(measureName);
        throw error;
      }
    };

    return descriptor;
  };
}

// React组件性能测量Hook
export function usePerformanceMeasure(name: string, dependencies: any[] = []) {
  React.useEffect(() => {
    performanceMonitor.startMeasure(name);

    return () => {
      performanceMonitor.endMeasure(name);
    };
  }, dependencies);
}

// 内存使用监控
export function getMemoryUsage(): MemoryInfo | null {
  // @ts-ignore
  if (performance.memory) {
    // @ts-ignore
    return performance.memory;
  }
  return null;
}

// 检查内存使用情况
export function checkMemoryUsage(): void {
  const memory = getMemoryUsage();
  if (!memory) return;

  const { usedJSHeapSize, totalJSHeapSize, jsHeapSizeLimit } = memory;
  const usagePercent = (usedJSHeapSize / jsHeapSizeLimit) * 100;

  if (
    usedJSHeapSize > PERFORMANCE_CONFIG.MONITORING.MEMORY_THRESHOLDS.WARNING
  ) {
    console.warn(
      `⚠️ High memory usage: ${(usedJSHeapSize / 1024 / 1024).toFixed(
        2
      )}MB (${usagePercent.toFixed(1)}%)`
    );
  }

  if (
    usedJSHeapSize > PERFORMANCE_CONFIG.MONITORING.MEMORY_THRESHOLDS.CRITICAL
  ) {
    console.error(
      `🚨 Critical memory usage: ${(usedJSHeapSize / 1024 / 1024).toFixed(
        2
      )}MB (${usagePercent.toFixed(1)}%)`
    );

    // 触发垃圾回收建议
    if (window.gc) {
      window.gc();
    }
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0;

  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
}

// 启动性能监控
export function startPerformanceMonitoring(): void {
  // 定期检查内存使用
  setInterval(checkMemoryUsage, PERFORMANCE_CONFIG.MEMORY.CLEANUP_INTERVAL);

  // 监控页面可见性变化
  document.addEventListener("visibilitychange", () => {
    if (document.hidden) {
      performanceMonitor.endMeasure("page-visible");
    } else {
      performanceMonitor.startMeasure("page-visible");
    }
  });

  console.log("🚀 Performance monitoring started");
}

// 导入React (如果在React环境中)
let React: any;
try {
  React = require("react");
} catch {
  // React不可用时的fallback
}
