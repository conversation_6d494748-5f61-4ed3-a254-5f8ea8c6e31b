/**
 * 状态管理集成测试
 * 测试React Query和Zustand的集成工作
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';

// Hooks导入
import { useEmails, useSendEmail, useUpdateEmail } from '../../hooks/useEmails';
import { useFolders } from '../../hooks/useFolders';
import { useCurrentUser, useLogin } from '../../hooks/useAuth';
import { useUIStore } from '../../store/uiStore';

// Mock API
import { apiService } from '../../services/api';
import { Folder } from '../../types';

vi.mock('../../services/api');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('状态管理集成测试', () => {
  let mockApiService: any;

  beforeEach(() => {
    mockApiService = apiService as any;
    vi.clearAllMocks();
  });

  afterEach(() => {
    // 清理Zustand store
    useUIStore.getState().clearSelection();
    useUIStore.getState().setCurrentFolder(null);
    useUIStore.getState().setCurrentEmail(null);
  });

  describe('邮件数据管理', () => {
    it('应该能够获取邮件列表', async () => {
      const mockEmails = [
        {
          id: 'email-1',
          subject: '测试邮件1',
          senderEmail: '<EMAIL>',
          senderName: '测试用户',
          recipients: ['<EMAIL>'],
          contentText: '邮件内容1',
          contentHtml: '<p>邮件内容1</p>',
          isRead: false,
          isStarred: false,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ];

      mockApiService.getEmails.mockResolvedValue({
        data: {
          success: true,
          data: {
            data: mockEmails,
            total: 1,
            page: 1,
            limit: 20,
            hasNext: false,
            hasPrev: false,
          },
        },
      } as any);

      const { result } = renderHook(
        () => useEmails({ folderId: 'inbox' }),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data?.data).toEqual(mockEmails);
      expect(mockApiService.getEmails).toHaveBeenCalledWith({
        folderId: 'inbox',
      });
    });

    it('应该能够发送邮件并更新缓存', async () => {
      const newEmail = {
        id: 'new-email-1',
        subject: '新邮件',
        senderEmail: '<EMAIL>',
        senderName: '当前用户',
        recipients: ['<EMAIL>'],
        contentText: '新邮件内容',
        contentHtml: '<p>新邮件内容</p>',
        isRead: true,
        isStarred: false,
        createdAt: '2024-01-01T12:00:00Z',
      };

      mockApiService.sendEmail.mockResolvedValue({
        data: { success: true, data: newEmail },
      } as any);

      const { result } = renderHook(
        () => useSendEmail(),
        { wrapper: createWrapper() }
      );

      await act(async () => {
        await result.current.mutateAsync({
          to: ['<EMAIL>'],
          subject: '新邮件',
          content: '新邮件内容',
        });
      });

      expect(mockApiService.sendEmail).toHaveBeenCalledWith({
        to: ['<EMAIL>'],
        subject: '新邮件',
        content: '新邮件内容',
      });
    });

    it('应该能够更新邮件状态（乐观更新）', async () => {
      const updatedEmail = {
        id: 'email-1',
        subject: '测试邮件1',
        senderEmail: '<EMAIL>',
        senderName: '测试用户',
        recipients: ['<EMAIL>'],
        contentText: '邮件内容1',
        contentHtml: '<p>邮件内容1</p>',
        isRead: true, // 更新为已读
        isStarred: false,
        createdAt: '2024-01-01T00:00:00Z',
      };

      mockApiService.updateEmail.mockResolvedValue({
        data: { success: true, data: updatedEmail },
      } as any);

      const { result } = renderHook(
        () => useUpdateEmail(),
        { wrapper: createWrapper() }
      );

      await act(async () => {
        await result.current.mutateAsync({
          id: 'email-1',
          updates: { isRead: true },
        });
      });

      expect(mockApiService.updateEmail).toHaveBeenCalledWith(
        'email-1',
        { isRead: true }
      );
    });
  });

  describe('文件夹数据管理', () => {
    it('应该能够获取文件夹列表', async () => {
      const mockFolders = [
        {
          id: 'inbox',
          name: '收件箱',
          type: 'inbox' as const,
          emailCount: 5,
          unreadCount: 2,
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'sent',
          name: '已发送',
          type: 'sent' as const,
          emailCount: 3,
          unreadCount: 0,
          createdAt: '2024-01-01T00:00:00Z',
        },
      ];

      mockApiService.getFolders.mockResolvedValue({
        data: { success: true, data: mockFolders },
      } as any);

      const { result } = renderHook(
        () => useFolders(),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockFolders);
      expect(mockApiService.getFolders).toHaveBeenCalled();
    });
  });

  describe('认证状态管理', () => {
    it('应该能够获取当前用户信息', async () => {
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        username: 'testuser',
        displayName: '测试用户',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
        isActive: true,
      };

      // Mock localStorage
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: vi.fn().mockReturnValue('mock-token'),
          setItem: vi.fn(),
          removeItem: vi.fn(),
        },
        writable: true,
      });

      mockApiService.getCurrentUser.mockResolvedValue({
        data: { success: true, data: mockUser },
      } as any);

      const { result } = renderHook(
        () => useCurrentUser(),
        { wrapper: createWrapper() }
      );

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUser);
      expect(mockApiService.getCurrentUser).toHaveBeenCalled();
    });

    it('应该能够登录并设置用户状态', async () => {
      const mockAuthResponse = {
        user: {
          id: 'user-1',
          email: '<EMAIL>',
          username: 'testuser',
          displayName: '测试用户',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
          isActive: true,
        },
        token: 'mock-jwt-token',
        refreshToken: 'mock-refresh-token',
      };

      mockApiService.login.mockResolvedValue({
        data: { success: true, data: mockAuthResponse },
      } as any);

      const { result } = renderHook(
        () => useLogin(),
        { wrapper: createWrapper() }
      );

      await act(async () => {
        await result.current.mutateAsync({
          email: '<EMAIL>',
          password: 'password123',
        });
      });

      expect(mockApiService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });

  describe('UI状态管理', () => {
    it('应该能够管理邮件选择状态', () => {
      const { result } = renderHook(() => useUIStore());

      // 初始状态
      expect(result.current.selectedEmails).toEqual([]);

      // 选择邮件
      act(() => {
        result.current.selectEmail('email-1');
        result.current.selectEmail('email-2');
      });

      expect(result.current.selectedEmails).toEqual(['email-1', 'email-2']);

      // 取消选择
      act(() => {
        result.current.unselectEmail('email-1');
      });

      expect(result.current.selectedEmails).toEqual(['email-2']);

      // 清除所有选择
      act(() => {
        result.current.clearSelection();
      });

      expect(result.current.selectedEmails).toEqual([]);
    });

    it('应该能够管理当前文件夹状态', () => {
      const { result } = renderHook(() => useUIStore());

      const mockFolder: Folder = {
        id: 'inbox',
        name: '收件箱',
        type: 'inbox' as const,
        emailCount: 5,
        unreadCount: 2,
        createdAt: '2024-01-01T00:00:00Z',
      };

      // 设置当前文件夹
      act(() => {
        result.current.setCurrentFolder(mockFolder);
      });

      expect(result.current.currentFolder).toEqual(mockFolder);

      // 清除当前文件夹
      act(() => {
        result.current.setCurrentFolder(null);
      });

      expect(result.current.currentFolder).toBeNull();
    });
  });
});
