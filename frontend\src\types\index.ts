// 用户相关类型
export interface User {
  id: string;
  email: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  permissions?: string[];
  role?: string;
}

// 邮件相关类型
export interface Email {
  id: string;
  messageId: string;
  userId: string;
  folderId: string;
  subject: string;
  senderEmail: string;
  senderName: string;
  recipients: string[];
  ccRecipients?: string[];
  bccRecipients?: string[];
  contentText: string;
  contentHtml: string;
  attachments: Attachment[];
  isRead: boolean;
  isStarred: boolean;
  isDeleted: boolean;
  receivedAt?: string;
  sentAt?: string;
  createdAt: string;
}

// 附件类型
export interface Attachment {
  id: string;
  filename: string;
  contentType: string;
  size: number;
  url: string;
}

// 文件夹类型
export interface Folder {
  id: string;
  userId?: string;
  name: string;
  type: 'inbox' | 'sent' | 'draft' | 'trash' | 'custom';
  parentId?: string;
  emailCount: number;
  unreadCount: number;
  createdAt: string;
}

// 联系人类型
export interface Contact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 认证相关类型
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  password: string;
  displayName: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

// 邮件撰写类型
export interface ComposeEmailRequest {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  content: string;
  attachments?: File[];
}

// 搜索类型
export interface SearchParams {
  query?: string;
  folder?: string;
  from?: string;
  to?: string;
  sender?: string;
  subject?: string;
  dateFrom?: string;
  dateTo?: string;
  hasAttachment?: boolean;
  isRead?: boolean;
  isStarred?: boolean;
}

// 应用状态类型
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  currentFolder: Folder | null;
  selectedEmails: string[];
  isLoading: boolean;
  error: string | null;
}

// 邮件列表状态
export interface EmailListState {
  emails: Email[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  page: number;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  createdAt: string;
}
