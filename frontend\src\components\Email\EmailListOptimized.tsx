import React, { useMemo, useCallback } from 'react';
import { List, Checkbox, Avatar, Typography, Button, Empty, Spin } from 'antd';
import {
  StarOutlined,
  StarFilled,
  PaperClipOutlined,
  DeleteOutlined,
  MailOutlined,
} from '@ant-design/icons';
import { FixedSizeList as VirtualList } from 'react-window';
import {
  useEmails,
  useUpdateEmail,
  useDeleteEmail,
} from '../../hooks/useEmails';
import { useUIStore } from '../../store/uiStore';
import { useNavigate, useParams } from 'react-router-dom';
import { showErrorMessage } from '../../hooks/useErrorHandler';
import type { Email } from '../../types';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const { Text, Title } = Typography;

interface EmailListOptimizedProps {
  folderId?: string;
  searchParams?: any;
  height?: number;
  itemHeight?: number;
}

interface EmailItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    emails: Email[];
    selectedEmails: string[];
    onEmailClick: (email: Email) => void;
    onSelectEmail: (emailId: string, checked: boolean) => void;
    onStarToggle: (email: Email, e: React.MouseEvent) => void;
    onMarkAsRead: (emailId: string, e: React.MouseEvent) => void;
    onDelete: (emailId: string, e: React.MouseEvent) => void;
  };
}

const EmailItem: React.FC<EmailItemProps> = ({ index, style, data }) => {
  const {
    emails,
    selectedEmails,
    onEmailClick,
    onSelectEmail,
    onStarToggle,
    onMarkAsRead,
    onDelete,
  } = data;

  const email = emails[index];

  if (!email) {
    return <div style={style} />;
  }

  const formatDate = (dateString: string) => {
    return formatDistanceToNow(new Date(dateString), {
      addSuffix: true,
      locale: zhCN,
    });
  };

  const getEmailPreview = (email: Email) => {
    const div = document.createElement('div');
    div.innerHTML = email.contentHtml || email.contentText;
    const text = div.textContent || div.innerText || '';
    return text.substring(0, 100) + (text.length > 100 ? '...' : '');
  };

  return (
    <div
      style={style}
      className={`cursor-pointer hover:bg-gray-50 transition-colors border-b border-gray-100 ${
        !email.isRead ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
      } ${selectedEmails.includes(email.id) ? 'bg-blue-100' : ''}`}
      onClick={() => onEmailClick(email)}
    >
      <div className="flex items-start space-x-3 p-4 w-full">
        <Checkbox
          checked={selectedEmails.includes(email.id)}
          onChange={(e) => onSelectEmail(email.id, e.target.checked)}
          onClick={(e) => e.stopPropagation()}
        />

        <Avatar size="small" className="bg-primary-500 flex-shrink-0">
          {email.senderName?.charAt(0) ||
            email.senderEmail.charAt(0).toUpperCase()}
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-1">
            <Text
              strong={!email.isRead}
              className="text-sm truncate"
              style={{ maxWidth: '200px' }}
            >
              {email.senderName || email.senderEmail}
            </Text>
            <div className="flex items-center space-x-2">
              {email.attachments.length > 0 && (
                <PaperClipOutlined className="text-gray-400 text-xs" />
              )}
              <Text className="text-xs text-gray-500">
                {formatDate(email.receivedAt || email.createdAt)}
              </Text>
            </div>
          </div>

          <div className="mb-1">
            <Text
              strong={!email.isRead}
              className="text-sm truncate block"
              style={{ maxWidth: '100%' }}
            >
              {email.subject || '(无主题)'}
            </Text>
          </div>

          <Text className="text-xs text-gray-500 truncate block">
            {getEmailPreview(email)}
          </Text>
        </div>

        <div className="flex items-center space-x-1 flex-shrink-0">
          <Button
            type="text"
            icon={
              email.isStarred ? (
                <StarFilled className="text-yellow-500" />
              ) : (
                <StarOutlined />
              )
            }
            onClick={(e) => onStarToggle(email, e)}
            size="small"
          />
          {!email.isRead && (
            <Button
              type="text"
              icon={<MailOutlined />}
              onClick={(e) => onMarkAsRead(email.id, e)}
              size="small"
              title="标记为已读"
            />
          )}
          <Button
            type="text"
            icon={<DeleteOutlined />}
            onClick={(e) => onDelete(email.id, e)}
            size="small"
            danger
            title="删除"
          />
        </div>
      </div>
    </div>
  );
};

const EmailListOptimized: React.FC<EmailListOptimizedProps> = ({
  folderId,
  searchParams,
  height = 600,
  itemHeight = 80,
}) => {
  const navigate = useNavigate();
  const { folderId: paramFolderId } = useParams();
  const currentFolderId = folderId || paramFolderId;

  const { selectedEmails, setSelectedEmails } = useUIStore();

  const {
    data: emailsData,
    isLoading,
    error,
  } = useEmails({
    folderId: currentFolderId,
    search: searchParams,
  });

  const updateEmailMutation = useUpdateEmail();
  const deleteEmailMutation = useDeleteEmail();

  const emails = useMemo(() => {
    return emailsData?.data || [];
  }, [emailsData]);

  const handleEmailClick = useCallback((email: Email) => {
    navigate(`/emails/${email.id}`);
  }, [navigate]);

  const handleSelectEmail = useCallback((emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmails([...selectedEmails, emailId]);
    } else {
      setSelectedEmails(selectedEmails.filter(id => id !== emailId));
    }
  }, [selectedEmails, setSelectedEmails]);

  const handleStarToggle = useCallback((email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    updateEmailMutation.mutate({
      id: email.id,
      updates: { isStarred: !email.isStarred },
    });
  }, [updateEmailMutation]);

  const handleMarkAsRead = useCallback((emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    updateEmailMutation.mutate({
      id: emailId,
      updates: { isRead: true },
    });
  }, [updateEmailMutation]);

  const handleDelete = useCallback((emailId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    deleteEmailMutation.mutate(emailId);
  }, [deleteEmailMutation]);

  const itemData = useMemo(() => ({
    emails,
    selectedEmails,
    onEmailClick: handleEmailClick,
    onSelectEmail: handleSelectEmail,
    onStarToggle: handleStarToggle,
    onMarkAsRead: handleMarkAsRead,
    onDelete: handleDelete,
  }), [
    emails,
    selectedEmails,
    handleEmailClick,
    handleSelectEmail,
    handleStarToggle,
    handleMarkAsRead,
    handleDelete,
  ]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    showErrorMessage(error, '加载邮件列表失败');
    return (
      <Empty
        description="加载邮件列表失败"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  if (!emails.length) {
    return (
      <Empty
        description="暂无邮件"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div className="email-list-optimized">
      <VirtualList
        height={height}
        itemCount={emails.length}
        itemSize={itemHeight}
        itemData={itemData}
        overscanCount={5}
      >
        {EmailItem}
      </VirtualList>
    </div>
  );
};

export default EmailListOptimized;
