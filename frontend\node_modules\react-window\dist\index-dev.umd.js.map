{"version": 3, "file": "index-dev.umd.js", "sources": ["../node_modules/@babel/runtime/helpers/esm/extends.js", "../node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "../node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../node_modules/@babel/runtime/helpers/esm/inheritsLoose.js", "../node_modules/memoize-one/dist/memoize-one.esm.js", "../src/timer.js", "../src/domHelpers.js", "../src/createGridComponent.js", "../src/VariableSizeGrid.js", "../src/createListComponent.js", "../src/VariableSizeList.js", "../src/FixedSizeGrid.js", "../src/FixedSizeList.js", "../node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../src/shallowDiffers.js", "../src/areEqual.js", "../src/shouldComponentUpdate.js"], "sourcesContent": ["export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf.js\";\nexport default function _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  setPrototypeOf(subClass, superClass);\n}", "var safeIsNaN = Number.isNaN ||\n    function ponyfill(value) {\n        return typeof value === 'number' && value !== value;\n    };\nfunction isEqual(first, second) {\n    if (first === second) {\n        return true;\n    }\n    if (safeIsNaN(first) && safeIsNaN(second)) {\n        return true;\n    }\n    return false;\n}\nfunction areInputsEqual(newInputs, lastInputs) {\n    if (newInputs.length !== lastInputs.length) {\n        return false;\n    }\n    for (var i = 0; i < newInputs.length; i++) {\n        if (!isEqual(newInputs[i], lastInputs[i])) {\n            return false;\n        }\n    }\n    return true;\n}\n\nfunction memoizeOne(resultFn, isEqual) {\n    if (isEqual === void 0) { isEqual = areInputsEqual; }\n    var lastThis;\n    var lastArgs = [];\n    var lastResult;\n    var calledOnce = false;\n    function memoized() {\n        var newArgs = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            newArgs[_i] = arguments[_i];\n        }\n        if (calledOnce && lastThis === this && isEqual(newArgs, lastArgs)) {\n            return lastResult;\n        }\n        lastResult = resultFn.apply(this, newArgs);\n        calledOnce = true;\n        lastThis = this;\n        lastArgs = newArgs;\n        return lastResult;\n    }\n    return memoized;\n}\n\nexport default memoizeOne;\n", "// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount, layout } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      // This is an edge case for lists; normally they only scroll in the dominant direction.\n      let scrollbarSize = 0;\n      if (this._outerRef) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (layout === 'vertical') {\n          scrollbarSize =\n            outerRef.scrollWidth > outerRef.clientWidth\n              ? getScrollbarSize()\n              : 0;\n        } else {\n          scrollbarSize =\n            outerRef.scrollHeight > outerRef.clientHeight\n              ? getScrollbarSize()\n              : 0;\n        }\n      }\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps,\n          scrollbarSize\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size + scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\ntype InstanceProps = any;\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) -\n        size +\n        ((itemSize: any): number) +\n        scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "names": ["_extends", "Object", "assign", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "this", "_assertThisInitialized", "self", "ReferenceError", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_inherits<PERSON><PERSON>e", "subClass", "superClass", "create", "constructor", "safeIsNaN", "Number", "isNaN", "value", "areInputsEqual", "newInputs", "lastInputs", "first", "second", "memoizeOne", "resultFn", "isEqual", "lastThis", "lastResult", "lastArgs", "calledOnce", "newArgs", "_i", "now", "performance", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "requestAnimationFrame", "tick", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "defaultItemKey", "columnIndex", "data", "rowIndex", "devWarningsOverscanCount", "devWarningsOverscanRowsColumnsCount", "devWarningsTagName", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_this", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "estimatedTotalHeight", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "estimatedTotalWidth", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "window", "WeakSet", "has", "add", "console", "warn", "Error", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "index", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "findNearestItem", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "devWarningsDirection", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "keys", "indexOf", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "nextState"], "mappings": "4OAAe,SAASA,WACtBA,EAAWC,OAAOC,QAAU,SAAUC,OAC/B,IAAIC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,KACrCG,EAASF,UAAUD,OAElB,IAAII,KAAOD,EACVN,OAAOQ,UAAUC,eAAeC,KAAKJ,EAAQC,KAC/CL,EAAOK,GAAOD,EAAOC,WAKpBL,IAGOS,MAAMC,KAAMR,WCff,SAASS,EAAuBC,WAChC,IAATA,QACI,IAAIC,eAAe,oEAGpBD,ECLM,SAASE,EAAgBC,EAAGC,UACzCF,EAAkBhB,OAAOmB,gBAAkB,SAAyBF,EAAGC,UACrED,EAAEG,UAAYF,EACPD,IAGcA,EAAGC,GCLb,SAASG,EAAeC,EAAUC,GAC/CD,EAASd,UAAYR,OAAOwB,OAAOD,EAAWf,WAC9Cc,EAASd,UAAUiB,YAAcH,EACjCH,EAAeG,EAAUC,GCJ3B,IAAIG,EAAYC,OAAOC,OACnB,SAAkBC,SACU,iBAAVA,GAAsBA,GAAUA,GAWtD,SAASC,EAAeC,EAAWC,MAC3BD,EAAU1B,SAAW2B,EAAW3B,cACzB,MAEN,IAAIF,EAAI,EAAGA,EAAI4B,EAAU1B,OAAQF,OAbzB8B,EAcIF,EAAU5B,GAdP+B,EAcWF,EAAW7B,KAbtC8B,IAAUC,GAGVR,EAAUO,IAAUP,EAAUQ,WAWnB,EAfnB,IAAiBD,EAAOC,SAkBb,EAGX,SAASC,EAAWC,EAAUC,OAEtBC,OADY,IAAZD,IAAsBA,EAAUP,OAGhCS,EADAC,EAAW,GAEXC,GAAa,4BAETC,EAAU,GACLC,EAAK,EAAGA,EAAKvC,UAAUC,OAAQsC,IACpCD,EAAQC,GAAMvC,UAAUuC,UAExBF,GAAcH,IAAa1B,MAAQyB,EAAQK,EAASF,KAGxDD,EAAaH,EAASzB,MAAMC,KAAM8B,GAClCD,GAAa,EACbH,EAAW1B,KACX4B,EAAWE,GALAH,GChCnB,IAGMK,EAFmB,iBAAhBC,aAAuD,mBAApBA,YAAYD,IAGpD,kBAAMC,YAAYD,OAClB,kBAAME,KAAKF,OAMR,SAASG,EAAcC,GAC5BC,qBAAqBD,EAAUE,IAG1B,SAASC,EAAeC,EAAoBC,OAC3CC,EAAQV,QAURI,EAAuB,CAC3BE,GAAIK,gCATGC,IACHZ,IAAQU,GAASD,EACnBD,EAAS1C,KAAK,MAEdsC,EAAUE,GAAKK,sBAAsBC,cAQlCR,ECjCT,IAAIS,GAAgB,EAGb,SAASC,EAAiBC,eAAAA,IAAAA,GAAwB,IACzC,IAAVF,GAAeE,EAAa,KACxBC,EAAMC,SAASC,cAAc,OAC7BC,EAAQH,EAAIG,MAClBA,EAAMC,MAAQ,OACdD,EAAME,OAAS,OACfF,EAAMG,SAAW,SAEfL,SAASM,KAA6BC,YAAYR,GAEpDH,EAAOG,EAAIS,YAAcT,EAAIU,YAE3BT,SAASM,KAA6BI,YAAYX,UAG/CH,EAQT,IAAIe,EAAwC,KAQrC,SAASC,EAAiBd,eAAAA,IAAAA,GAAwB,GAC/B,OAApBa,GAA4Bb,EAAa,KACrCe,EAAWb,SAASC,cAAc,OAClCa,EAAaD,EAASX,MAC5BY,EAAWX,MAAQ,OACnBW,EAAWV,OAAS,OACpBU,EAAWT,SAAW,SACtBS,EAAWC,UAAY,UAEjBC,EAAWhB,SAASC,cAAc,OAClCgB,EAAaD,EAASd,aAC5Be,EAAWd,MAAQ,QACnBc,EAAWb,OAAS,QAEpBS,EAASN,YAAYS,GAEnBhB,SAASM,KAA6BC,YAAYM,GAEhDA,EAASK,WAAa,EACxBP,EAAkB,uBAElBE,EAASK,WAAa,EAEpBP,EAD0B,IAAxBE,EAASK,WACO,WAEA,sBAIpBlB,SAASM,KAA6BI,YAAYG,GAE7CF,SAGFA,ECwET,IAEMQ,EAAiB,gBAAGC,IAAAA,cAAaC,cAAMC,aAC5BF,GAIbG,EAA2B,KAC3BC,EAAsC,KACtCC,EAAqB,KASV,SAASC,WACtBC,IAAAA,gBACAC,IAAAA,6BACAC,IAAAA,gCACAC,IAAAA,eACAC,IAAAA,wBACAC,IAAAA,uBACAC,IAAAA,+BACAC,IAAAA,4BACAC,IAAAA,aACAC,IAAAA,aACAC,IAAAA,0BACAC,IAAAA,6BACAC,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,8CAgDcC,8BACJA,UA9BRC,eAAsBJ,EAAkBK,EAAKF,cAC7CG,2BAA+C,OAC/CC,mBAQAC,MAAe,CACbC,cACAC,aAAa,EACbC,0BAA2B,UAC3BhC,WAC0C,iBAAjC0B,EAAKF,MAAMS,kBACdP,EAAKF,MAAMS,kBACX,EACNC,UACyC,iBAAhCR,EAAKF,MAAMW,iBACdT,EAAKF,MAAMW,iBACX,EACNC,0BAA0B,EAC1BC,wBAAyB,aA8Q3BC,8BAUAA,qBAAuBlF,GACrB,SACEmF,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,UAEEpB,EAAKF,MAAMuB,gBAAgD,CAC3DR,yBAAAA,EACAC,wBAAAA,EACAC,sBAAAA,EACAC,qBAAAA,EACAC,wBAAAA,EACAC,uBAAAA,EACAC,qBAAAA,EACAC,oBAAAA,SAINE,uBAOAA,cAAgB5F,GACd,SACE4C,EACAkC,EACAF,EACAK,EACAD,UAEEV,EAAKF,MAAMyB,SAAkC,CAC7CjB,0BAAAA,EACAhC,WAAAA,EACAkC,UAAAA,EACAG,wBAAAA,EACAD,yBAAAA,SAwDNc,uBACAA,cAAgB,SAAC9C,EAAkBF,OAW7BlB,IAV0C0C,EAAKF,MAA3C2B,IAAAA,YAAatD,IAAAA,UAAWuD,IAAAA,UAE1BC,EAAiB3B,EAAK4B,mBAC1BhC,GAAyC6B,EACzC7B,GAAyCzB,EACzCyB,GAAyC8B,GAGrC5H,EAAS4E,MAAYF,KAGvBmD,EAAe3H,eAAeF,GAChCwD,EAAQqE,EAAe7H,OAClB,KACC+H,EAAS9C,EACbiB,EAAKF,MACLtB,EACAwB,EAAKD,gBAED+B,EAAsB,QAAd3D,EACdwD,EAAe7H,GAAOwD,EAAQ,CAC5ByE,SAAU,WACVC,KAAMF,OAAQG,EAAYJ,EAC1BK,MAAOJ,EAAQD,OAASI,EACxBE,IAAK3C,EAAaQ,EAAKF,MAAOpB,EAAUsB,EAAKD,gBAC7CvC,OAAQ+B,EAAaS,EAAKF,MAAOpB,EAAUsB,EAAKD,gBAChDxC,MAAO2B,EAAec,EAAKF,MAAOtB,EAAawB,EAAKD,wBAIjDzC,KAGTsE,4BACAA,mBAAqBlG,GAAW,SAAC0G,EAAQC,EAASC,SAAc,QAkGhEC,UAAY,SAACC,SAQPA,EAAMC,cANRC,IAAAA,aACA7E,IAAAA,YACAS,IAAAA,WACAkC,IAAAA,UACAmC,IAAAA,aACAC,IAAAA,cAEGC,UAAS,SAAAC,MAEVA,EAAUxE,aAAeA,GACzBwE,EAAUtC,YAAcA,SAKjB,SAGDrC,EAAc6B,EAAKF,MAAnB3B,UAMJ4E,EAAuBzE,KACT,QAAdH,SACMH,SACD,WACH+E,GAAwBzE,YAErB,sBACHyE,EAAuBH,EAAc/E,EAAcS,EAMzDyE,EAAuBC,KAAKC,IAC1B,EACAD,KAAKE,IAAIH,EAAsBH,EAAc/E,QAEzCsF,EAAsBH,KAAKC,IAC/B,EACAD,KAAKE,IAAI1C,EAAWmC,EAAeD,UAG9B,CACLrC,aAAa,EACbC,0BACEwC,EAAUxE,WAAaA,EAAa,UAAY,WAClDA,WAAYyE,EACZvC,UAAW2C,EACXxC,wBACEmC,EAAUtC,UAAYA,EAAY,UAAY,WAChDE,0BAA0B,KAE3BV,EAAKoD,+BAGVC,gBAAkB,SAACC,OACTC,EAAavD,EAAKF,MAAlByD,WAEHrD,UAAcoD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAASvJ,eAAe,aAExBuJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCpD,EAAKC,4BACP3D,EAAc0D,EAAKC,8BAGhBA,2BAA6BvD,EAChCsD,EAAKyD,kBA/pB0B,QAoqBnCA,kBAAoB,aACbxD,2BAA6B,OAE7B4C,SAAS,CAAExC,aAAa,IAAS,aAG/BuB,oBAAoB,kBArlBtB8B,yBAAP,SACEC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BjD,EAAc8D,GACP,iCAGTE,SAAA,gBACEvF,IAAAA,WACAkC,IAAAA,eAKmByB,IAAf3D,IACFA,EAAa0E,KAAKC,IAAI,EAAG3E,SAET2D,IAAdzB,IACFA,EAAYwC,KAAKC,IAAI,EAAGzC,SAGrBqC,UAAS,SAAAC,eACOb,IAAf3D,IACFA,EAAawE,EAAUxE,iBAEP2D,IAAdzB,IACFA,EAAYsC,EAAUtC,WAItBsC,EAAUxE,aAAeA,GACzBwE,EAAUtC,YAAcA,EAEjB,KAGF,CACLF,0BACEwC,EAAUxE,WAAaA,EAAa,UAAY,WAClDA,WAAYA,EACZkC,UAAWA,EACXE,0BAA0B,EAC1BC,wBACEmC,EAAUtC,UAAYA,EAAY,UAAY,cAEjDrG,KAAKiJ,+BAGVU,aAAA,oBACEC,MAAAA,aAAQ,SACRvF,IAAAA,YACAE,IAAAA,WAMiDvE,KAAK2F,MAA9CkE,IAAAA,YAAaxG,IAAAA,OAAQyG,IAAAA,SAAU1G,IAAAA,QACLpD,KAAKgG,MAA/B7B,IAAAA,WAAYkC,IAAAA,UACd0D,EAAgBjH,SAEFgF,IAAhBzD,IACFA,EAAcwE,KAAKC,IAAI,EAAGD,KAAKE,IAAI1E,EAAawF,EAAc,UAE/C/B,IAAbvD,IACFA,EAAWsE,KAAKC,IAAI,EAAGD,KAAKE,IAAIxE,EAAUuF,EAAW,SAGjDE,EAAuBhF,EAC3BhF,KAAK2F,MACL3F,KAAK4F,gBAUDqE,EARsBhF,EAC1BjF,KAAK2F,MACL3F,KAAK4F,gBAOiBxC,EAAQ2G,EAAgB,EAC1CG,EACJF,EAAuB3G,EAAS0G,EAAgB,OAE7CL,SAAS,CACZvF,gBACkB2D,IAAhBzD,EACIa,EACElF,KAAK2F,MACLtB,EACAuF,EACAzF,EACAnE,KAAK4F,eACLsE,GAEF/F,EACNkC,eACeyB,IAAbvD,EACIY,EACEnF,KAAK2F,MACLpB,EACAqF,EACAvD,EACArG,KAAK4F,eACLqE,GAEF5D,OAIV8D,kBAAA,iBACkDnK,KAAK2F,MAA7CS,IAAAA,kBAAmBE,IAAAA,oBAEL,MAAlBtG,KAAK+F,UAAmB,KACpBqD,EAAapJ,KAAK+F,UACS,iBAAtBK,IACTgD,EAASjF,WAAaiC,GAEQ,iBAArBE,IACT8C,EAAS/C,UAAYC,QAIpB8D,yBAGPC,mBAAA,eACUrG,EAAchE,KAAK2F,MAAnB3B,YACoDhE,KAAKgG,MAAzD7B,IAAAA,WAAYkC,IAAAA,eAAWE,0BAEmB,MAAlBvG,KAAK+F,UAAmB,KAIhDqD,EAAapJ,KAAK+F,aACN,QAAd/B,SACMH,SACD,WACHuF,EAASjF,YAAcA,YAEpB,qBACHiF,EAASjF,WAAaA,oBAGdT,EAA6B0F,EAA7B1F,YAAa+E,EAAgBW,EAAhBX,YACrBW,EAASjF,WAAasE,EAAc/E,EAAcS,OAItDiF,EAASjF,WAAa0E,KAAKC,IAAI,EAAG3E,GAGpCiF,EAAS/C,UAAYwC,KAAKC,IAAI,EAAGzC,QAG9B+D,yBAGPE,qBAAA,WAC0C,OAApCtK,KAAK8F,4BACP3D,EAAcnC,KAAK8F,+BAIvByE,OAAA,iBAkBMvK,KAAK2F,MAhBP6E,IAAAA,SACAC,IAAAA,UACAZ,IAAAA,YACA7F,IAAAA,UACAX,IAAAA,OACAqH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACAC,IAAAA,aACAC,QAAAA,aAAU1G,IACV2G,IAAAA,iBACAC,IAAAA,aACAlB,IAAAA,SACA3G,IAAAA,MACA8H,IAAAA,eACA7H,IAAAA,MAEM8C,EAAgBlG,KAAKgG,MAArBE,cAKJlG,KAAKkL,8BAFPC,OACAC,SAEoCpL,KAAKqL,4BAApCC,OAAeC,OAEhBC,EAAQ,MACV3B,EAAc,GAAKC,MAEnB,IAAIvF,EAAW+G,EACf/G,GAAYgH,EACZhH,QAGE,IAAIF,EAAc8G,EAClB9G,GAAe+G,EACf/G,IAEAmH,EAAMC,KACJvI,gBAAcsH,EAAU,CACtBnG,YAAAA,EACAC,KAAMuG,EACN3E,YAAa+E,EAAiB/E,OAAc4B,EAC5CnI,IAAKmL,EAAQ,CAAEzG,YAAAA,EAAaC,KAAMuG,EAAUtG,SAAAA,IAC5CA,SAAAA,EACApB,MAAOnD,KAAKqH,cAAc9C,EAAUF,UASxC2F,EAAuBhF,EAC3BhF,KAAK2F,MACL3F,KAAK4F,gBAED8F,EAAsBzG,EAC1BjF,KAAK2F,MACL3F,KAAK4F,uBAGA1C,gBACL6H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACArD,SAAUpH,KAAKoI,UACfe,IAAKnJ,KAAKkJ,gBACV/F,SACEyE,SAAU,WACVvE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVqI,wBAAyB,QACzBC,WAAY,YACZ5H,UAAAA,GACGb,IAGPD,gBAAcyH,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVrC,IAAKuB,EACLvH,MAAO,CACLE,OAAQ2G,EACR6B,cAAe3F,EAAc,YAAS4B,EACtC1E,MAAOsI,SA+DftB,oBAAA,iBAC+DpK,KAAK2F,MAA1DkE,IAAAA,YAAa3C,IAAAA,gBAAiBE,IAAAA,SAAU0C,IAAAA,YAEjB,mBAApB5C,GACL2C,EAAc,GAAKC,EAAW,EAAG,OAM/B9J,KAAKkL,8BAJPxE,OACAC,OACAG,OACAC,SAOE/G,KAAKqL,4BAJPzE,OACAC,OACAG,OACAC,YAEGR,qBACHC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,MAKkB,mBAAbG,EAAyB,OAO9BpH,KAAKgG,MALPG,IAAAA,0BACAhC,IAAAA,WACAkC,IAAAA,UACAE,IAAAA,yBACAC,IAAAA,6BAEGW,cACHhD,EACAkC,EACAF,EACAK,EACAD,OA+CN2E,4BAAA,iBAOMlL,KAAK2F,MALPkE,IAAAA,YACAiC,IAAAA,oBACAC,IAAAA,qBACAC,IAAAA,cACAlC,IAAAA,WAE6D9J,KAAKgG,MAA5DG,IAAAA,0BAA2BD,IAAAA,YAAa/B,IAAAA,WAE1C8H,EACJH,GAAuBC,GAAwBC,GAAiB,KAE9C,IAAhBnC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGboC,EAAarH,EACjB7E,KAAK2F,MACLxB,EACAnE,KAAK4F,gBAEDuG,EAAYrH,EAChB9E,KAAK2F,MACLuG,EACA/H,EACAnE,KAAK4F,gBAKDwG,EACHlG,GAA6C,aAA9BC,EAEZ,EADA0C,KAAKC,IAAI,EAAGmD,GAEZI,EACHnG,GAA6C,YAA9BC,EAEZ,EADA0C,KAAKC,IAAI,EAAGmD,SAGX,CACLpD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIc,EAAc,EAAGsC,EAAYE,IAClDH,EACAC,MAIJd,0BAAA,iBAOMrL,KAAK2F,MALPkE,IAAAA,YACAmC,IAAAA,cACAM,IAAAA,iBACAC,IAAAA,kBACAzC,IAAAA,WAE0D9J,KAAKgG,MAAzDE,IAAAA,YAAaM,IAAAA,wBAAyBH,IAAAA,UAExC4F,EACJK,GAAoBC,GAAqBP,GAAiB,KAExC,IAAhBnC,GAAkC,IAAbC,QAChB,CAAC,EAAG,EAAG,EAAG,OAGboC,EAAa5G,EACjBtF,KAAK2F,MACLU,EACArG,KAAK4F,gBAEDuG,EAAY5G,EAChBvF,KAAK2F,MACLuG,EACA7F,EACArG,KAAK4F,gBAKDwG,EACHlG,GAA2C,aAA5BM,EAEZ,EADAqC,KAAKC,IAAI,EAAGmD,GAEZI,EACHnG,GAA2C,YAA5BM,EAEZ,EADAqC,KAAKC,IAAI,EAAGmD,SAGX,CACLpD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIe,EAAW,EAAGqC,EAAYE,IAC/CH,EACAC,OArhBuBK,kBAKpBC,aAAe,CACpBzI,UAAW,MACX6G,cAAU/C,EACVmD,gBAAgB,KAhDE,oBAAXyB,aAAoD,IAAnBA,OAAOC,UACjDnI,EAA2B,IAAImI,QAC/BlI,EAAsC,IAAIkI,QAC1CjI,EAAqB,IAAIiI,SAkqB7B,IAAMlD,EAAsB,kBAExBe,IAAAA,SACAxG,IAAAA,UACAX,IAAAA,OACAuH,IAAAA,aACAI,IAAAA,aACAe,IAAAA,qBACAC,IAAAA,cACAO,IAAAA,kBACAnJ,IAAAA,MAEA6C,IAAAA,YAG6B,iBAAlB+F,GACLxH,IAA6BA,EAAyBoI,IAAI3G,KAC5DzB,EAAyBqI,IAAI5G,GAC7B6G,QAAQC,KACN,uHAO4B,iBAAzBhB,GACsB,iBAAtBQ,GAGL9H,IACCA,EAAoCmI,IAAI3G,KAEzCxB,EAAoCoI,IAAI5G,GACxC6G,QAAQC,KACN,sJAMc,MAAhBnC,GAAwC,MAAhBI,GACtBtG,IAAuBA,EAAmBkI,IAAI3G,KAChDvB,EAAmBmI,IAAI5G,GACvB6G,QAAQC,KACN,sIAMU,MAAZvC,QACIwC,MACJ,uFAEmB,OAAbxC,EAAoB,cAAgBA,8BAItCxG,OACD,UACA,0BAIGgJ,MACJ,2FAEMhJ,yBAIS,iBAAVZ,QACH4J,MACJ,wFAEgB,OAAV5J,EAAiB,cAAgBA,0BAIrB,iBAAXC,QACH2J,MACJ,0FAEiB,OAAX3J,EAAkB,cAAgBA,wBCn3B1C2B,EAA0B,kBAC5B8E,IAAAA,SACAmD,IAAAA,eAAgBC,IAAAA,mBAAoBC,IAAAA,qBAElCC,EAA0B,KAI1BD,GAAwBrD,IAC1BqD,EAAuBrD,EAAW,GAGhCqD,GAAwB,EAAG,KACvBE,EAAeJ,EAAeE,GACpCC,EAA0BC,EAAa3F,OAAS2F,EAAaxK,YAMxDuK,GAHoBtD,EAAWqD,EAAuB,GACLD,GAKpDjI,EAAyB,kBAC3B4E,IAAAA,YAEAyD,IAAAA,kBACAC,IAAAA,qBACAC,IAAAA,wBAGEJ,EAA0B,KAI1BI,GAA2B3D,IAC7B2D,EAA0B3D,EAAc,GAGtC2D,GAA2B,EAAG,KAC1BH,EAAeC,EAAkBE,GACvCJ,EAA0BC,EAAa3F,OAAS2F,EAAaxK,YAMxDuK,GAHoBvD,EAAc2D,EAA0B,GACXD,GAKpDE,EAAkB,SACtBC,EACA/H,EACAgI,EACAC,OAEIC,EAAiBC,EAAUC,KACd,WAAbL,GACFG,EAAkBD,EAAcN,kBAChCQ,EAAanI,EAAM2B,YACnByG,EAAoBH,EAAcJ,0BAElCK,EAAkBD,EAAcX,eAChCa,EAAanI,EAAM4B,UACnBwG,EAAoBH,EAAcT,sBAGhCQ,EAAQI,EAAmB,KACzBrG,EAAS,KACTqG,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrCrG,EAAS2F,EAAa3F,OAAS2F,EAAaxK,SAGzC,IAAItD,EAAIwO,EAAoB,EAAGxO,GAAKoO,EAAOpO,IAAK,KAC/CsD,EAAOiL,EAASvO,GAEpBsO,EAAgBtO,GAAK,CACnBmI,OAAAA,EACA7E,KAAAA,GAGF6E,GAAU7E,EAGK,WAAb6K,EACFE,EAAcJ,wBAA0BG,EAExCC,EAAcT,qBAAuBQ,SAIlCE,EAAgBF,IAGnBK,EAAkB,SACtBN,EACA/H,EACAiI,EACAlG,OAEImG,EAAiBE,QACJ,WAAbL,GACFG,EAAkBD,EAAcN,kBAChCS,EAAoBH,EAAcJ,0BAElCK,EAAkBD,EAAcX,eAChCc,EAAoBH,EAAcT,uBAIlCY,EAAoB,EAAIF,EAAgBE,GAAmBrG,OAAS,IAExCA,EAErBuG,EACLP,EACA/H,EACAiI,EACAG,EACA,EACArG,GAMKwG,EACLR,EACA/H,EACAiI,EACA/E,KAAKC,IAAI,EAAGiF,GACZrG,IAKAuG,EAA8B,SAClCP,EACA/H,EACAiI,EACAO,EACAC,EACA1G,QAEO0G,GAAOD,GAAM,KACZE,EAASD,EAAMvF,KAAKyF,OAAOH,EAAOC,GAAO,GACzCG,EAAgBd,EACpBC,EACA/H,EACA0I,EACAT,GACAlG,UAEE6G,IAAkB7G,SACb2G,EACEE,EAAgB7G,EACzB0G,EAAMC,EAAS,EACNE,EAAgB7G,IACzByG,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvCR,EACA/H,EACAiI,EACAD,EACAjG,WAEM8G,EAAyB,WAAbd,EAAwB/H,EAAMkE,YAAclE,EAAMmE,SAChE2E,EAAW,EAGbd,EAAQa,GACRf,EAAgBC,EAAU/H,EAAOgI,EAAOC,GAAelG,OAASA,GAEhEiG,GAASc,EACTA,GAAY,SAGPR,EACLP,EACA/H,EACAiI,EACA/E,KAAKE,IAAI4E,EAAOa,EAAY,GAC5B3F,KAAKyF,MAAMX,EAAQ,GACnBjG,IAIEgH,EAAgC,SACpChB,EACA/H,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,OAEMlH,EAAoB,WAAb6K,EAAwB/H,EAAMvC,MAAQuC,EAAMtC,OACnDgK,EAAeI,EAAgBC,EAAU/H,EAAOgI,EAAOC,GAIvDgB,EACS,WAAblB,EACIzI,EAAuBU,EAAOiI,GAC9B5I,EAAwBW,EAAOiI,GAE/BiB,EAAYhG,KAAKC,IACrB,EACAD,KAAKE,IAAI6F,EAAqB/L,EAAMwK,EAAa3F,SAE7CoH,EAAYjG,KAAKC,IACrB,EACAuE,EAAa3F,OAAS7E,EAAOkH,EAAgBsD,EAAaxK,aAG9C,UAAV+G,IAEAA,EADE+E,GAAgBG,EAAYjM,GAAQ8L,GAAgBE,EAAYhM,EAC1D,OAEA,UAIJ+G,OACD,eACIiF,MACJ,aACIC,MACJ,gBACIjG,KAAKkG,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEG,EAAYD,GAIZF,EAAeG,EADjBA,EAIAD,IAKTG,EAAmBrK,EAAoB,CAC3CC,gBAAiB,SACfe,EACAgI,EACAC,UACWH,EAAgB,SAAU9H,EAAOgI,EAAOC,GAAelG,QAEpE7C,6BAA8B,SAC5Bc,EACAxB,EACAyJ,UACWI,EAAgB,SAAUrI,EAAOiI,EAAezJ,IAE7DW,gCAAiC,SAC/Ba,EACAuG,EACA/H,EACAyJ,WAEQ/D,EAAuBlE,EAAvBkE,YAAazG,EAAUuC,EAAVvC,MAEfiK,EAAeI,EACnB,SACA9H,EACAuG,EACA0B,GAEIiB,EAAY1K,EAAaf,EAE3BsE,EAAS2F,EAAa3F,OAAS2F,EAAaxK,KAC5CsJ,EAAYD,EAETC,EAAYtC,EAAc,GAAKnC,EAASmH,GAC7C1C,IACAzE,GAAU+F,EAAgB,SAAU9H,EAAOwG,EAAWyB,GAAe/K,YAGhEsJ,GAGTpH,eAAgB,SACdY,EACAgI,EACAC,UACWA,EAAcN,kBAAkBK,GAAO9K,MAEpDmC,wBAAAA,EACAC,uBAAAA,EAEAC,+BAAgC,SAC9BS,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,UAEA2E,EACE,SACA/I,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,IAGJ5E,4BAA6B,SAC3BQ,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,UAEA2E,EACE,MACA/I,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,IAGJ1E,aAAc,SACZM,EACAgI,EACAC,UACWH,EAAgB,MAAO9H,EAAOgI,EAAOC,GAAelG,QAEjEtC,aAAc,SACZO,EACAgI,EACAC,UACWA,EAAcX,eAAeU,GAAO9K,MAEjDyC,0BAA2B,SACzBK,EACAU,EACAuH,UACWI,EAAgB,MAAOrI,EAAOiI,EAAevH,IAE1Dd,6BAA8B,SAC5BI,EACAuG,EACA7F,EACAuH,WAEQ9D,EAAqBnE,EAArBmE,SAAUzG,EAAWsC,EAAXtC,OAEZgK,EAAeI,EACnB,MACA9H,EACAuG,EACA0B,GAEIiB,EAAYxI,EAAYhD,EAE1BqE,EAAS2F,EAAa3F,OAAS2F,EAAaxK,KAC5CsJ,EAAYD,EAETC,EAAYrC,EAAW,GAAKpC,EAASmH,GAC1C1C,IACAzE,GAAU+F,EAAgB,MAAO9H,EAAOwG,EAAWyB,GAAe/K,YAG7DsJ,GAGT3G,2BAAkBG,EAAmBM,SAI7BN,EAEAiI,EAAgB,CACpBN,kBAAmB,GACnBC,uBANAA,sBA9Z8B,GAqa9BL,qBANAA,oBA/Z8B,GAsa9BM,yBAA0B,EAC1BL,sBAAuB,EACvBF,eAAgB,WAGlBhH,EAASgJ,sBAAwB,SAC/B5K,EACA6K,YAAAA,IAAAA,GAA8B,GAE9BjJ,EAASkJ,kBAAkB,CAAE9K,YAAAA,EAAa6K,kBAAAA,KAG5CjJ,EAASmJ,mBAAqB,SAC5B7K,EACA2K,YAAAA,IAAAA,GAA8B,GAE9BjJ,EAASkJ,kBAAkB,CAAE5K,SAAAA,EAAU2K,kBAAAA,KAGzCjJ,EAASkJ,kBAAoB,gBAC3B9K,IAAAA,YACAE,IAAAA,aACA2K,kBAAAA,gBAM2B,iBAAhB7K,IACTuJ,EAAcJ,wBAA0B3E,KAAKE,IAC3C6E,EAAcJ,wBACdnJ,EAAc,IAGM,iBAAbE,IACTqJ,EAAcT,qBAAuBtE,KAAKE,IACxC6E,EAAcT,qBACd5I,EAAW,IAQf0B,EAASwB,oBAAoB,GAEzByH,GACFjJ,EAASoJ,eAINzB,GAGTnI,uCAAuC,EAEvCC,cAAe,gBAAG4B,IAAAA,YAAaC,IAAAA,aAEA,mBAAhBD,QACH0F,MACJ,mFAGoB,OAAhB1F,EAAuB,cAAgBA,uBAGxC,GAAyB,mBAAdC,QACVyF,MACJ,iFAEoB,OAAdzF,EAAqB,cAAgBA,0BCvX/CnD,EAAiB,SAACuJ,EAAerJ,UAAcqJ,GAIjD2B,EAAuB,KACvB5K,EAAqB,KAQV,SAAS6K,WACtBC,IAAAA,cACAC,IAAAA,sBACAC,IAAAA,YACAhB,IAAAA,8BACAiB,IAAAA,uBACAC,IAAAA,0BACApK,IAAAA,kBACAC,IAAAA,sCACAC,IAAAA,8CAuCcC,8BACJA,UA3BRC,eAAsBJ,EAAkBK,EAAKF,cAC7CI,mBACAD,2BAA+C,OAU/CE,MAAe,CACbC,cACAC,aAAa,EACb2J,gBAAiB,UACjBlB,aAC4C,iBAAnC9I,EAAKF,MAAMmK,oBACdjK,EAAKF,MAAMmK,oBACX,EACNvJ,0BAA0B,KAoN5BE,8BAMAA,qBAAuBlF,GACrB,SACEwO,EACAC,EACAC,EACAC,UAEErK,EAAKF,MAAMuB,gBAAgD,CAC3D6I,mBAAAA,EACAC,kBAAAA,EACAC,kBAAAA,EACAC,iBAAAA,SAIN/I,uBAKAA,cAAgB5F,GACd,SACEsO,EACAlB,EACApI,UAEEV,EAAKF,MAAMyB,SAAkC,CAC7CyI,gBAAAA,EACAlB,aAAAA,EACApI,yBAAAA,SAyCNc,uBACAA,cAAgB,SAACsG,OASXxK,IARoC0C,EAAKF,MAArC3B,IAAAA,UAAW8J,IAAAA,SAAUqC,IAAAA,OAEvB3I,EAAiB3B,EAAK4B,mBAC1BhC,GAAyCqI,EACzCrI,GAAyC0K,EACzC1K,GAAyCzB,MAIvCwD,EAAe3H,eAAe8N,GAChCxK,EAAQqE,EAAemG,OAClB,KACCjG,EAAS8H,EAAc3J,EAAKF,MAAOgI,EAAO9H,EAAKD,gBAC/C/C,EAAO6M,EAAY7J,EAAKF,MAAOgI,EAAO9H,EAAKD,gBAG3CwK,EACU,eAAdpM,GAAyC,eAAXmM,EAE1BxI,EAAsB,QAAd3D,EACRqM,EAAmBD,EAAe1I,EAAS,EACjDF,EAAemG,GAASxK,EAAQ,CAC9ByE,SAAU,WACVC,KAAMF,OAAQG,EAAYuI,EAC1BtI,MAAOJ,EAAQ0I,OAAmBvI,EAClCE,IAAMoI,EAAwB,EAAT1I,EACrBrE,OAAS+M,EAAsB,OAAPvN,EACxBO,MAAOgN,EAAevN,EAAO,eAI1BM,KAGTsE,4BACAA,mBAAqBlG,GAAW,SAAC0G,EAAQC,EAASC,SAAc,QAyChEmI,oBAAsB,SAACjI,SAC4BA,EAAMC,cAA/C5E,IAAAA,YAAaS,IAAAA,WAAYsE,IAAAA,cAC5BC,UAAS,SAAAC,MACRA,EAAUgG,eAAiBxK,SAItB,SAGDH,EAAc6B,EAAKF,MAAnB3B,UAEJ2K,EAAexK,KACD,QAAdH,SAKMH,SACD,WACH8K,GAAgBxK,YAEb,sBACHwK,EAAelG,EAAc/E,EAAcS,SAMjDwK,EAAe9F,KAAKC,IAClB,EACAD,KAAKE,IAAI4F,EAAclG,EAAc/E,IAGhC,CACLwC,aAAa,EACb2J,gBACElH,EAAUgG,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACApI,0BAA0B,KAE3BV,EAAKoD,+BAGVsH,kBAAoB,SAAClI,SAC+BA,EAAMC,cAAhDC,IAAAA,aAAcC,IAAAA,aAAcnC,IAAAA,YAC/BqC,UAAS,SAAAC,MACRA,EAAUgG,eAAiBtI,SAItB,SAIHsI,EAAe9F,KAAKC,IACxB,EACAD,KAAKE,IAAI1C,EAAWmC,EAAeD,UAG9B,CACLrC,aAAa,EACb2J,gBACElH,EAAUgG,aAAeA,EAAe,UAAY,WACtDA,aAAAA,EACApI,0BAA0B,KAE3BV,EAAKoD,+BAGVC,gBAAkB,SAACC,OACTC,EAAavD,EAAKF,MAAlByD,WAEHrD,UAAcoD,EAEK,mBAAbC,EACTA,EAASD,GAEG,MAAZC,GACoB,iBAAbA,GACPA,EAASvJ,eAAe,aAExBuJ,EAASC,QAAUF,MAIvBF,2BAA6B,WACa,OAApCpD,EAAKC,4BACP3D,EAAc0D,EAAKC,8BAGhBA,2BAA6BvD,EAChCsD,EAAKyD,kBAngB0B,QAwgBnCA,kBAAoB,aACbxD,2BAA6B,OAE7B4C,SAAS,CAAExC,aAAa,IAAS,aAG/BuB,oBAAoB,EAAG,qBA3czB8B,yBAAP,SACEC,EACAb,UAEAc,EAAoBD,EAAWb,GAC/BjD,EAAc8D,GACP,iCAGTE,SAAA,SAASiF,GACPA,EAAe9F,KAAKC,IAAI,EAAG6F,QAEtBjG,UAAS,SAAAC,UACRA,EAAUgG,eAAiBA,EACtB,KAEF,CACLkB,gBACElH,EAAUgG,aAAeA,EAAe,UAAY,WACtDA,aAAcA,EACdpI,0BAA0B,KAE3BvG,KAAKiJ,+BAGVU,aAAA,SAAagE,EAAe/D,YAAAA,IAAAA,EAAuB,cACnB5J,KAAK2F,MAA3B6I,IAAAA,UAAW2B,IAAAA,OACXxB,EAAiB3O,KAAKgG,MAAtB2I,aAERhB,EAAQ9E,KAAKC,IAAI,EAAGD,KAAKE,IAAI4E,EAAOa,EAAY,QAK5CzE,EAAgB,KAChB/J,KAAK+F,UAAW,KACZqD,EAAapJ,KAAK+F,UAEtBgE,EADa,aAAXoG,EAEA/G,EAASX,YAAcW,EAAS1F,YAC5BZ,IACA,EAGJsG,EAASZ,aAAeY,EAASb,aAC7BzF,IACA,OAIL4G,SACHgF,EACE1O,KAAK2F,MACLgI,EACA/D,EACA+E,EACA3O,KAAK4F,eACLmE,OAKNI,kBAAA,iBACqDnK,KAAK2F,MAAhD3B,IAAAA,UAAW8L,IAAAA,oBAAqBK,IAAAA,UAEL,iBAAxBL,GAAsD,MAAlB9P,KAAK+F,UAAmB,KAC/DqD,EAAapJ,KAAK+F,UAEN,eAAd/B,GAAyC,eAAXmM,EAChC/G,EAASjF,WAAa2L,EAEtB1G,EAAS/C,UAAYyJ,OAIpB1F,yBAGPC,mBAAA,iBACgCrK,KAAK2F,MAA3B3B,IAAAA,UAAWmM,IAAAA,SACgCnQ,KAAKgG,MAAhD2I,IAAAA,kBAAcpI,0BAE4B,MAAlBvG,KAAK+F,UAAmB,KAChDqD,EAAapJ,KAAK+F,aAGN,eAAd/B,GAAyC,eAAXmM,KACd,QAAdnM,SAIMH,SACD,WACHuF,EAASjF,YAAcwK,YAEpB,qBACHvF,EAASjF,WAAawK,oBAGdjL,EAA6B0F,EAA7B1F,YAAa+E,EAAgBW,EAAhBX,YACrBW,EAASjF,WAAasE,EAAc/E,EAAciL,OAItDvF,EAASjF,WAAawK,OAGxBvF,EAAS/C,UAAYsI,OAIpBvE,yBAGPE,qBAAA,WAC0C,OAApCtK,KAAK8F,4BACP3D,EAAcnC,KAAK8F,+BAIvByE,OAAA,iBAkBMvK,KAAK2F,MAhBP6E,IAAAA,SACAC,IAAAA,UACAzG,IAAAA,UACAX,IAAAA,OACAqH,IAAAA,SACAC,IAAAA,iBACAC,IAAAA,aACA4D,IAAAA,UACA3D,IAAAA,aACAC,QAAAA,aAAU1G,IACV+L,IAAAA,OACApF,IAAAA,iBACAC,IAAAA,aACA7H,IAAAA,MACA8H,IAAAA,eACA7H,IAAAA,MAEM8C,EAAgBlG,KAAKgG,MAArBE,YAGFkK,EACU,eAAdpM,GAAyC,eAAXmM,EAE1B/I,EAAWgJ,EACbpQ,KAAKsQ,oBACLtQ,KAAKuQ,oBAEuBvQ,KAAKwQ,oBAA9BtE,OAAYC,OAEbX,EAAQ,MACVgD,EAAY,MACT,IAAIb,EAAQzB,EAAYyB,GAASxB,EAAWwB,IAC/CnC,EAAMC,KACJvI,gBAAcsH,EAAU,CACtBlG,KAAMuG,EACNlL,IAAKmL,EAAQ6C,EAAO9C,GACpB8C,MAAAA,EACAzH,YAAa+E,EAAiB/E,OAAc4B,EAC5C3E,MAAOnD,KAAKqH,cAAcsG,UAQ5BiB,EAAqBa,EACzBzP,KAAK2F,MACL3F,KAAK4F,uBAGA1C,gBACL6H,GAAoBC,GAAgB,MACpC,CACEP,UAAAA,EACArD,SAAAA,EACA+B,IAAKnJ,KAAKkJ,gBACV/F,SACEyE,SAAU,WACVvE,OAAAA,EACAD,MAAAA,EACAE,SAAU,OACVqI,wBAAyB,QACzBC,WAAY,YACZ5H,UAAAA,GACGb,IAGPD,gBAAcyH,GAAoBC,GAAgB,MAAO,CACvDJ,SAAUgB,EACVrC,IAAKuB,EACLvH,MAAO,CACLE,OAAQ+M,EAAe,OAASxB,EAChC/C,cAAe3F,EAAc,YAAS4B,EACtC1E,MAAOgN,EAAexB,EAAqB,cA6CnDxE,oBAAA,cAC4C,mBAA/BpK,KAAK2F,MAAMuB,iBACElH,KAAK2F,MAAnB6I,UACQ,EAAG,OAMbxO,KAAKwQ,oBAJPT,OACAC,OACAC,OACAC,YAEGzJ,qBACHsJ,EACAC,EACAC,EACAC,MAK6B,mBAAxBlQ,KAAK2F,MAAMyB,SAAyB,OAKzCpH,KAAKgG,MAHP6J,IAAAA,gBACAlB,IAAAA,aACApI,IAAAA,8BAEGY,cACH0I,EACAlB,EACApI,OAgDNiK,kBAAA,iBACuCxQ,KAAK2F,MAAlC6I,IAAAA,UAAWxC,IAAAA,gBACoChM,KAAKgG,MAApDE,IAAAA,YAAa2J,IAAAA,gBAAiBlB,IAAAA,gBAEpB,IAAdH,QACK,CAAC,EAAG,EAAG,EAAG,OAGbtC,EAAayD,EACjB3P,KAAK2F,MACLgJ,EACA3O,KAAK4F,gBAEDuG,EAAYyD,EAChB5P,KAAK2F,MACLuG,EACAyC,EACA3O,KAAK4F,gBAKDwG,EACHlG,GAAmC,aAApB2J,EAEZ,EADAhH,KAAKC,IAAI,EAAGkD,GAEZK,EACHnG,GAAmC,YAApB2J,EAEZ,EADAhH,KAAKC,IAAI,EAAGkD,SAGX,CACLnD,KAAKC,IAAI,EAAGoD,EAAaE,GACzBvD,KAAKC,IAAI,EAAGD,KAAKE,IAAIyF,EAAY,EAAGrC,EAAYE,IAChDH,EACAC,OA/XuBK,kBAKpBC,aAAe,CACpBzI,UAAW,MACX6G,cAAU/C,EACVqI,OAAQ,WACRnE,cAAe,EACff,gBAAgB,KArCE,oBAAXyB,aAAoD,IAAnBA,OAAOC,UACjD2C,EAAuB,IAAI3C,QAC3BjI,EAAqB,IAAIiI,SA+gB7B,IAAMlD,EAAsB,kBAExBe,IAAAA,SACAxG,IAAAA,UACAX,IAAAA,OACA8M,IAAAA,OACAvF,IAAAA,aACAI,IAAAA,aACA5H,IAAAA,MAEA6C,IAAAA,SAGoB,MAAhB2E,GAAwC,MAAhBI,GACtBtG,IAAuBA,EAAmBkI,IAAI3G,KAChDvB,EAAmBmI,IAAI5G,GACvB6G,QAAQC,KACN,0IAOAqD,EAA6B,eAAdpM,GAAyC,eAAXmM,SAE3CnM,OACD,iBACA,WACCsL,IAAyBA,EAAqB1C,IAAI3G,KACpDqJ,EAAqBzC,IAAI5G,GACzB6G,QAAQC,KACN,oKAKD,UACA,0BAIGC,MACJ,2FAEMhJ,6BAIJmM,OACD,iBACA,+BAIGnD,MACJ,oGAEMmD,yBAII,MAAZ3F,QACIwC,MACJ,uFAEmB,OAAbxC,EAAoB,cAAgBA,0BAI1C4F,GAAiC,iBAAVhN,QACnB4J,MACJ,mGAEgB,OAAV5J,EAAiB,cAAgBA,uBAEpC,IAAKgN,GAAkC,iBAAX/M,QAC3B2J,MACJ,mGAEiB,OAAX3J,EAAkB,cAAgBA,wBC3sB1CoK,EAAkB,SACtB9H,EACAgI,EACAC,OAEQE,EAAenI,EAAfmI,SACAD,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,qBAErBJ,EAAQI,EAAmB,KACzBrG,EAAS,KACTqG,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrCrG,EAAS2F,EAAa3F,OAAS2F,EAAaxK,SAGzC,IAAItD,EAAIwO,EAAoB,EAAGxO,GAAKoO,EAAOpO,IAAK,KAC/CsD,EAASiL,EAAgCvO,GAE7CsO,EAAgBtO,GAAK,CACnBmI,OAAAA,EACA7E,KAAAA,GAGF6E,GAAU7E,EAGZ+K,EAAcG,kBAAoBJ,SAG7BE,EAAgBF,IAmCnBM,EAA8B,SAClCtI,EACAiI,EACAO,EACAC,EACA1G,QAEO0G,GAAOD,GAAM,KACZE,EAASD,EAAMvF,KAAKyF,OAAOH,EAAOC,GAAO,GACzCG,EAAgBd,EAAgB9H,EAAO0I,EAAQT,GAAelG,UAEhE6G,IAAkB7G,SACb2G,EACEE,EAAgB7G,EACzB0G,EAAMC,EAAS,EACNE,EAAgB7G,IACzByG,EAAOE,EAAS,UAIhBD,EAAM,EACDA,EAAM,EAEN,GAILF,EAAmC,SACvCvI,EACAiI,EACAD,EACAjG,WAEQ8G,EAAc7I,EAAd6I,UACJC,EAAW,EAGbd,EAAQa,GACRf,EAAgB9H,EAAOgI,EAAOC,GAAelG,OAASA,GAEtDiG,GAASc,EACTA,GAAY,SAGPR,EACLtI,EACAiI,EACA/E,KAAKE,IAAI4E,EAAOa,EAAY,GAC5B3F,KAAKyF,MAAMX,EAAQ,GACnBjG,IAIE+H,EAAwB,kBAC1BjB,IAAAA,UACAX,IAAAA,gBAAiB4C,IAAAA,kBAAmB1C,IAAAA,kBAElC2C,EAA2B,KAI3B3C,GAAqBS,IACvBT,EAAoBS,EAAY,GAG9BT,GAAqB,EAAG,KACpBV,EAAeQ,EAAgBE,GACrC2C,EAA2BrD,EAAa3F,OAAS2F,EAAaxK,YAMzD6N,GAHoBlC,EAAYT,EAAoB,GACH0C,GAKpDE,EAAmBpB,EAAoB,CAC3CC,cAAe,SACb7J,EACAgI,EACAC,UACWH,EAAgB9H,EAAOgI,EAAOC,GAAelG,QAE1DgI,YAAa,SACX/J,EACAgI,EACAC,UACWA,EAAcC,gBAAgBF,GAAO9K,MAElD4M,sBAAAA,EAEAf,8BAA+B,SAC7B/I,EACAgI,EACA/D,EACA+E,EACAf,EACA7D,OAEQ/F,EAAqC2B,EAArC3B,UAAWX,EAA0BsC,EAA1BtC,OAAQ8M,EAAkBxK,EAAlBwK,OAAQ/M,EAAUuC,EAAVvC,MAI7BP,EAD6B,eAAdmB,GAAyC,eAAXmM,EACpB/M,EAAQC,EACjCgK,EAAeI,EAAgB9H,EAAOgI,EAAOC,GAI7CgB,EAAqBa,EAAsB9J,EAAOiI,GAElDiB,EAAYhG,KAAKC,IACrB,EACAD,KAAKE,IAAI6F,EAAqB/L,EAAMwK,EAAa3F,SAE7CoH,EAAYjG,KAAKC,IACrB,EACAuE,EAAa3F,OAAS7E,EAAOwK,EAAaxK,KAAOkH,UAGrC,UAAVH,IAKAA,EAHA+E,GAAgBG,EAAYjM,GAC5B8L,GAAgBE,EAAYhM,EAEpB,OAEA,UAIJ+G,OACD,eACIiF,MACJ,aACIC,MACJ,gBACIjG,KAAKkG,MAAMD,GAAaD,EAAYC,GAAa,OACrD,sBAECH,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfc,uBAAwB,SACtBhK,EACA+B,EACAkG,UAxLoB,SACtBjI,EACAiI,EACAlG,OAEQmG,EAAuCD,EAAvCC,gBAAiBE,EAAsBH,EAAtBG,yBAGvBA,EAAoB,EAAIF,EAAgBE,GAAmBrG,OAAS,IAExCA,EAErBuG,EACLtI,EACAiI,EACAG,EACA,EACArG,GAMKwG,EACLvI,EACAiI,EACA/E,KAAKC,IAAI,EAAGiF,GACZrG,GA8JSsG,CAAgBrI,EAAOiI,EAAelG,IAEnDkI,0BAA2B,SACzBjK,EACAuG,EACAyC,EACAf,WAEQ5J,EAAgD2B,EAAhD3B,UAAWX,EAAqCsC,EAArCtC,OAAQmL,EAA6B7I,EAA7B6I,UAAW2B,EAAkBxK,EAAlBwK,OAAQ/M,EAAUuC,EAAVvC,MAIxCP,EAD6B,eAAdmB,GAAyC,eAAXmM,EACpB/M,EAAQC,EACjCgK,EAAeI,EAAgB9H,EAAOuG,EAAY0B,GAClDiB,EAAYF,EAAe9L,EAE7B6E,EAAS2F,EAAa3F,OAAS2F,EAAaxK,KAC5CsJ,EAAYD,EAETC,EAAYqC,EAAY,GAAK9G,EAASmH,GAC3C1C,IACAzE,GAAU+F,EAAgB9H,EAAOwG,EAAWyB,GAAe/K,YAGtDsJ,GAGT3G,2BAAkBG,EAAmBM,OAG7B2H,EAAgB,CACpBC,gBAAiB,GACjB4C,kBAJ8B9K,EAAxB8K,mBAxQwB,GA6Q9B1C,mBAAoB,UAGtB9H,EAAS2K,gBAAkB,SACzBjD,EACAuB,YAAAA,IAAAA,GAA8B,GAE9BtB,EAAcG,kBAAoBlF,KAAKE,IACrC6E,EAAcG,kBACdJ,EAAQ,GAOV1H,EAASwB,oBAAoB,GAEzByH,GACFjJ,EAASoJ,eAINzB,GAGTnI,uCAAuC,EAEvCC,cAAe,gBAAGoI,IAAAA,YAEU,mBAAbA,QACHd,MACJ,gFAEmB,OAAbc,EAAoB,cAAgBA,0BC/S9C+C,EAAgBlM,EAAoB,CACxCC,gBAAiB,WAA8B+I,UAC7CA,IADkBrG,aAGpBvC,eAAgB,WAA8B4I,YAA3BrG,aAGnBjC,aAAc,WAA4BsI,UACxCA,IADepG,WAGjBnC,aAAc,WAA4BuI,YAAzBpG,WAGjBvC,wBAAyB,gBAAG8E,IAAAA,kBAAUvC,UACPuC,GAE/B7E,uBAAwB,gBAAG4E,IAAAA,qBAAavC,YACPuC,GAEjC3E,+BAAgC,WAE9Bb,EACAuF,EACAzF,EACAyJ,EACA7D,OALEF,IAAAA,YAAavC,IAAAA,YAAalE,IAAAA,MAOtB0N,EAAmBjI,KAAKC,IAC5B,EACAe,EAAgBvC,EAA6BlE,GAEzCyL,EAAYhG,KAAKE,IACrB+H,EACAzM,EAAgBiD,GAEZwH,EAAYjG,KAAKC,IACrB,EACAzE,EAAgBiD,EACdlE,EACA2G,EACEzC,UAGQ,UAAVsC,IAEAA,EADEzF,GAAc2K,EAAY1L,GAASe,GAAc0K,EAAYzL,EACvD,OAEA,UAIJwG,OACD,eACIiF,MACJ,aACIC,MACJ,aAGGiC,EAAelI,KAAKkG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAelI,KAAKmI,KAAK5N,EAAQ,GAC5B,EACE2N,EAAeD,EAAmBjI,KAAKyF,MAAMlL,EAAQ,GACvD0N,EAEAC,MAEN,sBAEC5M,GAAc2K,GAAa3K,GAAc0K,EACpC1K,EACE2K,EAAYD,GAIZ1K,EAAa2K,EADfA,EAIAD,IAKf1J,4BAA6B,WAE3BZ,EACAqF,EACAvD,EACAuH,EACA7D,OALExC,IAAAA,UAAWlE,IAAAA,OAAQyG,IAAAA,SAOfmH,EAAgBpI,KAAKC,IACzB,EACAgB,EAAavC,EAA2BlE,GAEpCwL,EAAYhG,KAAKE,IACrBkI,EACA1M,EAAagD,GAETuH,EAAYjG,KAAKC,IACrB,EACAvE,EAAagD,EACXlE,EACA0G,EACExC,UAGQ,UAAVqC,IAEAA,EADEvD,GAAayI,EAAYzL,GAAUgD,GAAawI,EAAYxL,EACtD,OAEA,UAIJuG,OACD,eACIiF,MACJ,aACIC,MACJ,aAGGiC,EAAelI,KAAKkG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAelI,KAAKmI,KAAK3N,EAAS,GAC7B,EACE0N,EAAeE,EAAgBpI,KAAKyF,MAAMjL,EAAS,GACrD4N,EAEAF,MAEN,sBAEC1K,GAAayI,GAAazI,GAAawI,EAClCxI,EACEyI,EAAYD,GAIZxI,EAAYyI,EADdA,EAIAD,IAKfhK,6BAA8B,WAE5BV,OADEmD,IAAAA,YAAauC,IAAAA,mBAGfhB,KAAKC,IACH,EACAD,KAAKE,IACHc,EAAc,EACdhB,KAAKyF,MAAMnK,EAAemD,MAIhCxC,gCAAiC,WAE/BoH,EACA/H,OAFEmD,IAAAA,YAAauC,IAAAA,YAAazG,IAAAA,MAItByE,EAAOqE,EAAe5E,EACtB4J,EAAoBrI,KAAKmI,MAC5B5N,EAAQe,EAAa0D,GAAUP,UAE3BuB,KAAKC,IACV,EACAD,KAAKE,IACHc,EAAc,EACdqC,EAAagF,EAAoB,KAKvC5L,0BAA2B,WAEzBe,OADEkB,IAAAA,UAAWuC,IAAAA,gBAGbjB,KAAKC,IACH,EACAD,KAAKE,IAAIe,EAAW,EAAGjB,KAAKyF,MAAMjI,EAAckB,MAGpDhC,6BAA8B,WAE5B2G,EACA7F,OAFEkB,IAAAA,UAAWuC,IAAAA,SAAUzG,IAAAA,OAIjB2E,EAAMkE,EAAe3E,EACrB4J,EAAiBtI,KAAKmI,MACzB3N,EAASgD,EAAY2B,GAAST,UAE1BsB,KAAKC,IACV,EACAD,KAAKE,IACHe,EAAW,EACXoC,EAAaiF,EAAiB,KAKpC3L,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,gBAAG4B,IAAAA,YAAaC,IAAAA,aAEA,iBAAhBD,QACH0F,MACJ,iFAGoB,OAAhB1F,EAAuB,cAAgBA,0BAKtB,iBAAdC,QACHyF,MACJ,+EAEoB,OAAdzF,EAAqB,cAAgBA,0BCpO/C6J,EAAgB7B,EAAoB,CACxCC,cAAe,WAA2B7B,UACxCA,IADgBG,UAGlB4B,YAAa,WAA2B/B,YAAxBG,UAGhB2B,sBAAuB,gBAAGjB,IAAAA,mBAAWV,SACPU,GAE9BE,8BAA+B,WAE7Bf,EACA/D,EACA+E,EACAf,EACA7D,OALE/F,IAAAA,UAAWX,IAAAA,OAAQmL,IAAAA,UAAWV,IAAAA,SAAUqC,IAAAA,OAAQ/M,IAAAA,MAS5CP,EAD6B,eAAdmB,GAAyC,eAAXmM,EACpB/M,EAAQC,EACjCgO,EAAiBxI,KAAKC,IAC1B,EACA0F,EAAcV,EAA0BjL,GAEpCgM,EAAYhG,KAAKE,IACrBsI,EACA1D,EAAUG,GAENgB,EAAYjG,KAAKC,IACrB,EACA6E,EAAUG,EACRjL,EACEiL,EACF/D,UAGU,UAAVH,IAKAA,EAHA+E,GAAgBG,EAAYjM,GAC5B8L,GAAgBE,EAAYhM,EAEpB,OAEA,UAIJ+G,OACD,eACIiF,MACJ,aACIC,MACJ,aAGGiC,EAAelI,KAAKkG,MACxBD,GAAaD,EAAYC,GAAa,UAEpCiC,EAAelI,KAAKmI,KAAKnO,EAAO,GAC3B,EACEkO,EAAeM,EAAiBxI,KAAKyF,MAAMzL,EAAO,GACpDwO,EAEAN,MAGN,sBAECpC,GAAgBG,GAAaH,GAAgBE,EACxCF,EACEA,EAAeG,EACjBA,EAEAD,IAKfc,uBAAwB,WAEtBjI,OADE8G,IAAAA,UAAWV,IAAAA,gBAGbjF,KAAKC,IACH,EACAD,KAAKE,IAAIyF,EAAY,EAAG3F,KAAKyF,MAAM5G,EAAWoG,MAGlD8B,0BAA2B,WAEzB1D,EACAyC,OAFE3K,IAAAA,UAAWX,IAAAA,OAAQmL,IAAAA,UAAWV,IAAAA,SAAUqC,IAAAA,OAAQ/M,IAAAA,MAM5CsE,EAASwE,EAAe4B,EACxBjL,EAF6B,eAAdmB,GAAyC,eAAXmM,EAEpB/M,EAAQC,EACjCiO,EAAkBzI,KAAKmI,MAC1BnO,EAAO8L,EAAejH,GAAYoG,UAE9BjF,KAAKC,IACV,EACAD,KAAKE,IACHyF,EAAY,EACZtC,EAAaoF,EAAkB,KAKrC9L,2BAAkBG,KAIlBF,uCAAuC,EAEvCC,cAAe,gBAAGoI,IAAAA,YAEU,iBAAbA,QACHd,MACJ,8EAEmB,OAAbc,EAAoB,cAAgBA,0BCjIrC,SAASyD,EAA8B7R,EAAQ8R,MAC9C,MAAV9R,EAAgB,MAAO,OAGvBC,EAAKJ,EAFLD,EAAS,GACTmS,EAAarS,OAAOsS,KAAKhS,OAGxBH,EAAI,EAAGA,EAAIkS,EAAWhS,OAAQF,IACjCI,EAAM8R,EAAWlS,GACbiS,EAASG,QAAQhS,IAAQ,IAC7BL,EAAOK,GAAOD,EAAOC,WAGhBL,ECRM,SAASsS,EAAeC,EAAcC,OAC9C,IAAIC,KAAaF,OACdE,KAAaD,UACV,MAGN,IAAIC,KAAaD,KAChBD,EAAKE,KAAeD,EAAKC,UACpB,SAGJ,8BCRM,SAASC,EACtBC,EACAzI,OAEe0I,EAA2BD,EAAlC9O,MAAqBgP,IAAaF,KAC3BG,EAA2B5I,EAAlCrG,MAAqBkP,IAAa7I,YAGvCoI,EAAeM,EAAWE,KAAeR,EAAeO,EAAUE,sHCPxD,SACb7I,EACA8I,UAGGN,EAAShS,KAAK2F,MAAO6D,IAAcoI,EAAe5R,KAAKgG,MAAOsM"}