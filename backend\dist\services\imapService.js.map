{"version": 3, "file": "imapService.js", "sourceRoot": "", "sources": ["../../src/services/imapService.ts"], "names": [], "mappings": ";;AAAA,uCAAoC;AACpC,2CAA0C;AAuB1C,MAAM,WAAW;IAAjB;QACU,WAAM,GAAoB,IAAI,CAAC;IAqGzC,CAAC;IAnGC,KAAK,CAAC,OAAO,CAAC,MAAkB;QAC9B,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,GAAG,IAAI,mBAAQ,CAAC;gBACzB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACrB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAkB,OAAO,EAAE,QAAgB,EAAE;QAC3D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YAEvD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE;oBACxC,QAAQ,EAAE,IAAI;oBACd,aAAa,EAAE,IAAI;oBACnB,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,IAAI;iBACZ,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAEd,MAAM,MAAM,GAAmB,EAAE,CAAC;gBAElC,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;oBACrC,IAAI,CAAC;wBACH,MAAM,MAAM,GAAG,MAAM,IAAA,yBAAY,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAElD,MAAM,KAAK,GAAiB;4BAC1B,EAAE,EAAE,OAAO,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;4BACrD,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,YAAY;4BACvC,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,IAAI,SAAS;4BACpC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC3C,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;4BAC/B,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,EAAE;4BACtC,WAAW,EAAE,MAAM,CAAC,WAAW;4BAC/B,KAAK,EAAE,OAAO,CAAC,KAAK,IAAI,EAAE;yBAC3B,CAAC;wBAEF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACrB,CAAC;oBAAC,OAAO,UAAU,EAAE,CAAC;wBACpB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,UAAU,CAAC,CAAC;oBACpD,CAAC;gBACH,CAAC;gBAED,OAAO,MAAM,CAAC;YAChB,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB;QACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;YAC5D,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAED,kBAAe,IAAI,WAAW,EAAE,CAAC"}