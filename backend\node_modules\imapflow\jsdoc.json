{"templates": {"referenceTitle": "ImapFlow", "disableSort": false, "collapse": true, "resources": {"Source Code": "https://github.com/postalsys/imapflow"}, "cleverLinks": true, "monospaceLinks": false, "default": {"outputSourceFiles": false}, "search": {"apiKey": "082c6635b32d44ed095369a5f1c790fd", "indexName": "imapflow", "hitsPerPage": 7}}, "plugins": ["plugins/markdown"], "opts": {"destination": "./docs/", "encoding": "utf8", "private": true, "recurse": true, "template": "./node_modules/imapflow-jsdoc-template"}}