{"version": 3, "file": "index.esm.js", "sources": ["../src/timer.js", "../src/domHelpers.js", "../src/createGridComponent.js", "../src/VariableSizeGrid.js", "../src/createListComponent.js", "../src/VariableSizeList.js", "../src/FixedSizeGrid.js", "../src/FixedSizeList.js", "../src/shallowDiffers.js", "../src/areEqual.js", "../src/shouldComponentUpdate.js"], "sourcesContent": ["// @flow\n\n// Animation frame based implementation of setTimeout.\n// Inspired by <PERSON>, https://gist.github.com/joelambert/1002116#file-requesttimeout-js\n\nconst hasNativePerformanceNow =\n  typeof performance === 'object' && typeof performance.now === 'function';\n\nconst now = hasNativePerformanceNow\n  ? () => performance.now()\n  : () => Date.now();\n\nexport type TimeoutID = {|\n  id: AnimationFrameID,\n|};\n\nexport function cancelTimeout(timeoutID: TimeoutID) {\n  cancelAnimationFrame(timeoutID.id);\n}\n\nexport function requestTimeout(callback: Function, delay: number): TimeoutID {\n  const start = now();\n\n  function tick() {\n    if (now() - start >= delay) {\n      callback.call(null);\n    } else {\n      timeoutID.id = requestAnimationFrame(tick);\n    }\n  }\n\n  const timeoutID: TimeoutID = {\n    id: requestAnimationFrame(tick),\n  };\n\n  return timeoutID;\n}\n", "// @flow\n\nlet size: number = -1;\n\n// This utility copied from \"dom-helpers\" package.\nexport function getScrollbarSize(recalculate?: boolean = false): number {\n  if (size === -1 || recalculate) {\n    const div = document.createElement('div');\n    const style = div.style;\n    style.width = '50px';\n    style.height = '50px';\n    style.overflow = 'scroll';\n\n    ((document.body: any): HTMLBodyElement).appendChild(div);\n\n    size = div.offsetWidth - div.clientWidth;\n\n    ((document.body: any): HTMLBodyElement).removeChild(div);\n  }\n\n  return size;\n}\n\nexport type RTLOffsetType =\n  | 'negative'\n  | 'positive-descending'\n  | 'positive-ascending';\n\nlet cachedRTLResult: RTLOffsetType | null = null;\n\n// TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n// Chrome does not seem to adhere; its scrollLeft values are positive (measured relative to the left).\n// <PERSON><PERSON>'s elastic bounce makes detecting this even more complicated wrt potential false positives.\n// The safest way to check this is to intentionally set a negative offset,\n// and then verify that the subsequent \"scroll\" event matches the negative offset.\n// If it does not match, then we can assume a non-standard RTL scroll implementation.\nexport function getRTLOffsetType(recalculate?: boolean = false): RTLOffsetType {\n  if (cachedRTLResult === null || recalculate) {\n    const outerDiv = document.createElement('div');\n    const outerStyle = outerDiv.style;\n    outerStyle.width = '50px';\n    outerStyle.height = '50px';\n    outerStyle.overflow = 'scroll';\n    outerStyle.direction = 'rtl';\n\n    const innerDiv = document.createElement('div');\n    const innerStyle = innerDiv.style;\n    innerStyle.width = '100px';\n    innerStyle.height = '100px';\n\n    outerDiv.appendChild(innerDiv);\n\n    ((document.body: any): HTMLBodyElement).appendChild(outerDiv);\n\n    if (outerDiv.scrollLeft > 0) {\n      cachedRTLResult = 'positive-descending';\n    } else {\n      outerDiv.scrollLeft = 1;\n      if (outerDiv.scrollLeft === 0) {\n        cachedRTLResult = 'negative';\n      } else {\n        cachedRTLResult = 'positive-ascending';\n      }\n    }\n\n    ((document.body: any): HTMLBodyElement).removeChild(outerDiv);\n\n    return cachedRTLResult;\n  }\n\n  return cachedRTLResult;\n}\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\ntype Direction = 'ltr' | 'rtl';\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n\ntype RenderComponentProps<T> = {|\n  columnIndex: number,\n  data: T,\n  isScrolling?: boolean,\n  rowIndex: number,\n  style: Object,\n|};\nexport type RenderComponent<T> = React$ComponentType<\n  $Shape<RenderComponentProps<T>>\n>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype OnItemsRenderedCallback = ({\n  overscanColumnStartIndex: number,\n  overscanColumnStopIndex: number,\n  overscanRowStartIndex: number,\n  overscanRowStopIndex: number,\n  visibleColumnStartIndex: number,\n  visibleColumnStopIndex: number,\n  visibleRowStartIndex: number,\n  visibleRowStopIndex: number,\n}) => void;\ntype OnScrollCallback = ({\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [key: string]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  columnCount: number,\n  columnWidth: itemSize,\n  direction: Direction,\n  height: number,\n  initialScrollLeft?: number,\n  initialScrollTop?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemData: T,\n  itemKey?: (params: {|\n    columnIndex: number,\n    data: T,\n    rowIndex: number,\n  |}) => any,\n  onItemsRendered?: OnItemsRenderedCallback,\n  onScroll?: OnScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanColumnCount?: number,\n  overscanColumnsCount?: number, // deprecated\n  overscanCount?: number, // deprecated\n  overscanRowCount?: number,\n  overscanRowsCount?: number, // deprecated\n  rowCount: number,\n  rowHeight: itemSize,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  horizontalScrollDirection: ScrollDirection,\n  scrollLeft: number,\n  scrollTop: number,\n  scrollUpdateWasRequested: boolean,\n  verticalScrollDirection: ScrollDirection,\n|};\n\ntype getItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype getEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForItemAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any,\n  scrollbarSize: number\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = ({ columnIndex, data, rowIndex }) =>\n  `${rowIndex}:${columnIndex}`;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsOverscanCount = null;\nlet devWarningsOverscanRowsColumnsCount = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsOverscanCount = new WeakSet();\n    devWarningsOverscanRowsColumnsCount = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createGridComponent({\n  getColumnOffset,\n  getColumnStartIndexForOffset,\n  getColumnStopIndexForStartIndex,\n  getColumnWidth,\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n  getOffsetForColumnAndAlignment,\n  getOffsetForRowAndAlignment,\n  getRowHeight,\n  getRowOffset,\n  getRowStartIndexForOffset,\n  getRowStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getColumnOffset: getItemOffset,\n  getColumnStartIndexForOffset: GetStartIndexForOffset,\n  getColumnStopIndexForStartIndex: GetStopIndexForStartIndex,\n  getColumnWidth: getItemSize,\n  getEstimatedTotalHeight: getEstimatedTotalSize,\n  getEstimatedTotalWidth: getEstimatedTotalSize,\n  getOffsetForColumnAndAlignment: GetOffsetForItemAndAlignment,\n  getOffsetForRowAndAlignment: GetOffsetForItemAndAlignment,\n  getRowOffset: getItemOffset,\n  getRowHeight: getItemSize,\n  getRowStartIndexForOffset: GetStartIndexForOffset,\n  getRowStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class Grid<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n    _outerRef: ?HTMLDivElement;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      horizontalScrollDirection: 'forward',\n      scrollLeft:\n        typeof this.props.initialScrollLeft === 'number'\n          ? this.props.initialScrollLeft\n          : 0,\n      scrollTop:\n        typeof this.props.initialScrollTop === 'number'\n          ? this.props.initialScrollTop\n          : 0,\n      scrollUpdateWasRequested: false,\n      verticalScrollDirection: 'forward',\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo({\n      scrollLeft,\n      scrollTop,\n    }: {\n      scrollLeft: number,\n      scrollTop: number,\n    }): void {\n      if (scrollLeft !== undefined) {\n        scrollLeft = Math.max(0, scrollLeft);\n      }\n      if (scrollTop !== undefined) {\n        scrollTop = Math.max(0, scrollTop);\n      }\n\n      this.setState(prevState => {\n        if (scrollLeft === undefined) {\n          scrollLeft = prevState.scrollLeft;\n        }\n        if (scrollTop === undefined) {\n          scrollTop = prevState.scrollTop;\n        }\n\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          return null;\n        }\n\n        return {\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: scrollLeft,\n          scrollTop: scrollTop,\n          scrollUpdateWasRequested: true,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem({\n      align = 'auto',\n      columnIndex,\n      rowIndex,\n    }: {\n      align: ScrollToAlign,\n      columnIndex?: number,\n      rowIndex?: number,\n    }): void {\n      const { columnCount, height, rowCount, width } = this.props;\n      const { scrollLeft, scrollTop } = this.state;\n      const scrollbarSize = getScrollbarSize();\n\n      if (columnIndex !== undefined) {\n        columnIndex = Math.max(0, Math.min(columnIndex, columnCount - 1));\n      }\n      if (rowIndex !== undefined) {\n        rowIndex = Math.max(0, Math.min(rowIndex, rowCount - 1));\n      }\n\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      // The scrollbar size should be considered when scrolling an item into view,\n      // to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      const horizontalScrollbarSize =\n        estimatedTotalWidth > width ? scrollbarSize : 0;\n      const verticalScrollbarSize =\n        estimatedTotalHeight > height ? scrollbarSize : 0;\n\n      this.scrollTo({\n        scrollLeft:\n          columnIndex !== undefined\n            ? getOffsetForColumnAndAlignment(\n                this.props,\n                columnIndex,\n                align,\n                scrollLeft,\n                this._instanceProps,\n                verticalScrollbarSize\n              )\n            : scrollLeft,\n        scrollTop:\n          rowIndex !== undefined\n            ? getOffsetForRowAndAlignment(\n                this.props,\n                rowIndex,\n                align,\n                scrollTop,\n                this._instanceProps,\n                horizontalScrollbarSize\n              )\n            : scrollTop,\n      });\n    }\n\n    componentDidMount() {\n      const { initialScrollLeft, initialScrollTop } = this.props;\n\n      if (this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (typeof initialScrollLeft === 'number') {\n          outerRef.scrollLeft = initialScrollLeft;\n        }\n        if (typeof initialScrollTop === 'number') {\n          outerRef.scrollTop = initialScrollTop;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction } = this.props;\n      const { scrollLeft, scrollTop, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // So we need to determine which browser behavior we're dealing with, and mimic it.\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              outerRef.scrollLeft = -scrollLeft;\n              break;\n            case 'positive-ascending':\n              outerRef.scrollLeft = scrollLeft;\n              break;\n            default:\n              const { clientWidth, scrollWidth } = outerRef;\n              outerRef.scrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        } else {\n          outerRef.scrollLeft = Math.max(0, scrollLeft);\n        }\n\n        outerRef.scrollTop = Math.max(0, scrollTop);\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        columnCount,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemData,\n        itemKey = defaultItemKey,\n        outerElementType,\n        outerTagName,\n        rowCount,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      const [\n        columnStartIndex,\n        columnStopIndex,\n      ] = this._getHorizontalRangeToRender();\n      const [rowStartIndex, rowStopIndex] = this._getVerticalRangeToRender();\n\n      const items = [];\n      if (columnCount > 0 && rowCount) {\n        for (\n          let rowIndex = rowStartIndex;\n          rowIndex <= rowStopIndex;\n          rowIndex++\n        ) {\n          for (\n            let columnIndex = columnStartIndex;\n            columnIndex <= columnStopIndex;\n            columnIndex++\n          ) {\n            items.push(\n              createElement(children, {\n                columnIndex,\n                data: itemData,\n                isScrolling: useIsScrolling ? isScrolling : undefined,\n                key: itemKey({ columnIndex, data: itemData, rowIndex }),\n                rowIndex,\n                style: this._getItemStyle(rowIndex, columnIndex),\n              })\n            );\n          }\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalHeight = getEstimatedTotalHeight(\n        this.props,\n        this._instanceProps\n      );\n      const estimatedTotalWidth = getEstimatedTotalWidth(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll: this._onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: estimatedTotalHeight,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: estimatedTotalWidth,\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanColumnStartIndex: number,\n      overscanColumnStopIndex: number,\n      overscanRowStartIndex: number,\n      overscanRowStopIndex: number,\n      visibleColumnStartIndex: number,\n      visibleColumnStopIndex: number,\n      visibleRowStartIndex: number,\n      visibleRowStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanColumnStartIndex: number,\n        overscanColumnStopIndex: number,\n        overscanRowStartIndex: number,\n        overscanRowStopIndex: number,\n        visibleColumnStartIndex: number,\n        visibleColumnStopIndex: number,\n        visibleRowStartIndex: number,\n        visibleRowStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): OnItemsRenderedCallback)({\n          overscanColumnStartIndex,\n          overscanColumnStopIndex,\n          overscanRowStartIndex,\n          overscanRowStopIndex,\n          visibleColumnStartIndex,\n          visibleColumnStopIndex,\n          visibleRowStartIndex,\n          visibleRowStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollLeft: number,\n      scrollTop: number,\n      horizontalScrollDirection: ScrollDirection,\n      verticalScrollDirection: ScrollDirection,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollLeft: number,\n        scrollTop: number,\n        horizontalScrollDirection: ScrollDirection,\n        verticalScrollDirection: ScrollDirection,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): OnScrollCallback)({\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          verticalScrollDirection,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      const { columnCount, onItemsRendered, onScroll, rowCount } = this.props;\n\n      if (typeof onItemsRendered === 'function') {\n        if (columnCount > 0 && rowCount > 0) {\n          const [\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n          ] = this._getHorizontalRangeToRender();\n          const [\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex,\n          ] = this._getVerticalRangeToRender();\n          this._callOnItemsRendered(\n            overscanColumnStartIndex,\n            overscanColumnStopIndex,\n            overscanRowStartIndex,\n            overscanRowStopIndex,\n            visibleColumnStartIndex,\n            visibleColumnStopIndex,\n            visibleRowStartIndex,\n            visibleRowStopIndex\n          );\n        }\n      }\n\n      if (typeof onScroll === 'function') {\n        const {\n          horizontalScrollDirection,\n          scrollLeft,\n          scrollTop,\n          scrollUpdateWasRequested,\n          verticalScrollDirection,\n        } = this.state;\n        this._callOnScroll(\n          scrollLeft,\n          scrollTop,\n          horizontalScrollDirection,\n          verticalScrollDirection,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (rowIndex: number, columnIndex: number) => Object;\n    _getItemStyle = (rowIndex: number, columnIndex: number): Object => {\n      const { columnWidth, direction, rowHeight } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && columnWidth,\n        shouldResetStyleCacheOnItemSizeChange && direction,\n        shouldResetStyleCacheOnItemSizeChange && rowHeight\n      );\n\n      const key = `${rowIndex}:${columnIndex}`;\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(key)) {\n        style = itemStyleCache[key];\n      } else {\n        const offset = getColumnOffset(\n          this.props,\n          columnIndex,\n          this._instanceProps\n        );\n        const isRtl = direction === 'rtl';\n        itemStyleCache[key] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offset,\n          right: isRtl ? offset : undefined,\n          top: getRowOffset(this.props, rowIndex, this._instanceProps),\n          height: getRowHeight(this.props, rowIndex, this._instanceProps),\n          width: getColumnWidth(this.props, columnIndex, this._instanceProps),\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getHorizontalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanColumnCount,\n        overscanColumnsCount,\n        overscanCount,\n        rowCount,\n      } = this.props;\n      const { horizontalScrollDirection, isScrolling, scrollLeft } = this.state;\n\n      const overscanCountResolved: number =\n        overscanColumnCount || overscanColumnsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getColumnStartIndexForOffset(\n        this.props,\n        scrollLeft,\n        this._instanceProps\n      );\n      const stopIndex = getColumnStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollLeft,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || horizontalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || horizontalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(columnCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _getVerticalRangeToRender(): [number, number, number, number] {\n      const {\n        columnCount,\n        overscanCount,\n        overscanRowCount,\n        overscanRowsCount,\n        rowCount,\n      } = this.props;\n      const { isScrolling, verticalScrollDirection, scrollTop } = this.state;\n\n      const overscanCountResolved: number =\n        overscanRowCount || overscanRowsCount || overscanCount || 1;\n\n      if (columnCount === 0 || rowCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getRowStartIndexForOffset(\n        this.props,\n        scrollTop,\n        this._instanceProps\n      );\n      const stopIndex = getRowStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollTop,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || verticalScrollDirection === 'backward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n      const overscanForward =\n        !isScrolling || verticalScrollDirection === 'forward'\n          ? Math.max(1, overscanCountResolved)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(rowCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScroll = (event: ScrollEvent): void => {\n      const {\n        clientHeight,\n        clientWidth,\n        scrollLeft,\n        scrollTop,\n        scrollHeight,\n        scrollWidth,\n      } = event.currentTarget;\n      this.setState(prevState => {\n        if (\n          prevState.scrollLeft === scrollLeft &&\n          prevState.scrollTop === scrollTop\n        ) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n        // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n        // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n        // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n        let calculatedScrollLeft = scrollLeft;\n        if (direction === 'rtl') {\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              calculatedScrollLeft = -scrollLeft;\n              break;\n            case 'positive-descending':\n              calculatedScrollLeft = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        calculatedScrollLeft = Math.max(\n          0,\n          Math.min(calculatedScrollLeft, scrollWidth - clientWidth)\n        );\n        const calculatedScrollTop = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          horizontalScrollDirection:\n            prevState.scrollLeft < scrollLeft ? 'forward' : 'backward',\n          scrollLeft: calculatedScrollLeft,\n          scrollTop: calculatedScrollTop,\n          verticalScrollDirection:\n            prevState.scrollTop < scrollTop ? 'forward' : 'backward',\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1);\n      });\n    };\n  };\n}\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    innerTagName,\n    outerTagName,\n    overscanColumnsCount,\n    overscanCount,\n    overscanRowsCount,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (typeof overscanCount === 'number') {\n      if (devWarningsOverscanCount && !devWarningsOverscanCount.has(instance)) {\n        devWarningsOverscanCount.add(instance);\n        console.warn(\n          'The overscanCount prop has been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (\n      typeof overscanColumnsCount === 'number' ||\n      typeof overscanRowsCount === 'number'\n    ) {\n      if (\n        devWarningsOverscanRowsColumnsCount &&\n        !devWarningsOverscanRowsColumnsCount.has(instance)\n      ) {\n        devWarningsOverscanRowsColumnsCount.add(instance);\n        console.warn(\n          'The overscanColumnsCount and overscanRowsCount props have been deprecated. ' +\n            'Please use the overscanColumnCount and overscanRowCount props instead.'\n        );\n      }\n    }\n\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    switch (direction) {\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    if (typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Grids must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    }\n\n    if (typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Grids must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\ntype ItemType = 'column' | 'row';\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype ItemMetadataMap = { [index: number]: ItemMetadata };\ntype InstanceProps = {|\n  columnMetadataMap: ItemMetadataMap,\n  estimatedColumnWidth: number,\n  estimatedRowHeight: number,\n  lastMeasuredColumnIndex: number,\n  lastMeasuredRowIndex: number,\n  rowMetadataMap: ItemMetadataMap,\n|};\n\nconst getEstimatedTotalHeight = (\n  { rowCount }: Props<any>,\n  { rowMetadataMap, estimatedRowHeight, lastMeasuredRowIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredRowIndex >= rowCount) {\n    lastMeasuredRowIndex = rowCount - 1;\n  }\n\n  if (lastMeasuredRowIndex >= 0) {\n    const itemMetadata = rowMetadataMap[lastMeasuredRowIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = rowCount - lastMeasuredRowIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedRowHeight;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getEstimatedTotalWidth = (\n  { columnCount }: Props<any>,\n  {\n    columnMetadataMap,\n    estimatedColumnWidth,\n    lastMeasuredColumnIndex,\n  }: InstanceProps\n) => {\n  let totalSizeOfMeasuredRows = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredColumnIndex >= columnCount) {\n    lastMeasuredColumnIndex = columnCount - 1;\n  }\n\n  if (lastMeasuredColumnIndex >= 0) {\n    const itemMetadata = columnMetadataMap[lastMeasuredColumnIndex];\n    totalSizeOfMeasuredRows = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = columnCount - lastMeasuredColumnIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedColumnWidth;\n\n  return totalSizeOfMeasuredRows + totalSizeOfUnmeasuredItems;\n};\n\nconst getItemMetadata = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  let itemMetadataMap, itemSize, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    itemSize = ((props.columnWidth: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    itemSize = ((props.rowHeight: any): itemSizeGetter);\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = itemSize(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    if (itemType === 'column') {\n      instanceProps.lastMeasuredColumnIndex = index;\n    } else {\n      instanceProps.lastMeasuredRowIndex = index;\n    }\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  let itemMetadataMap, lastMeasuredIndex;\n  if (itemType === 'column') {\n    itemMetadataMap = instanceProps.columnMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredColumnIndex;\n  } else {\n    itemMetadataMap = instanceProps.rowMetadataMap;\n    lastMeasuredIndex = instanceProps.lastMeasuredRowIndex;\n  }\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      itemType,\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      itemType,\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(\n      itemType,\n      props,\n      middle,\n      instanceProps\n    ).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  itemType: ItemType,\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const itemCount = itemType === 'column' ? props.columnCount : props.rowCount;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(itemType, props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    itemType,\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getOffsetForIndexAndAlignment = (\n  itemType: ItemType,\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: InstanceProps,\n  scrollbarSize: number\n): number => {\n  const size = itemType === 'column' ? props.width : props.height;\n  const itemMetadata = getItemMetadata(itemType, props, index, instanceProps);\n\n  // Get estimated total size after ItemMetadata is computed,\n  // To ensure it reflects actual measurements instead of just estimates.\n  const estimatedTotalSize =\n    itemType === 'column'\n      ? getEstimatedTotalWidth(props, instanceProps)\n      : getEstimatedTotalHeight(props, instanceProps);\n\n  const maxOffset = Math.max(\n    0,\n    Math.min(estimatedTotalSize - size, itemMetadata.offset)\n  );\n  const minOffset = Math.max(\n    0,\n    itemMetadata.offset - size + scrollbarSize + itemMetadata.size\n  );\n\n  if (align === 'smart') {\n    if (scrollOffset >= minOffset - size && scrollOffset <= maxOffset + size) {\n      align = 'auto';\n    } else {\n      align = 'center';\n    }\n  }\n\n  switch (align) {\n    case 'start':\n      return maxOffset;\n    case 'end':\n      return minOffset;\n    case 'center':\n      return Math.round(minOffset + (maxOffset - minOffset) / 2);\n    case 'auto':\n    default:\n      if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n        return scrollOffset;\n      } else if (minOffset > maxOffset) {\n        // Because we only take into account the scrollbar size when calculating minOffset\n        // this value can be larger than maxOffset when at the end of the list\n        return minOffset;\n      } else if (scrollOffset < minOffset) {\n        return minOffset;\n      } else {\n        return maxOffset;\n      }\n  }\n};\n\nconst VariableSizeGrid = createGridComponent({\n  getColumnOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('column', props, index, instanceProps).offset,\n\n  getColumnStartIndexForOffset: (\n    props: Props<any>,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('column', props, instanceProps, scrollLeft),\n\n  getColumnStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollLeft: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { columnCount, width } = props;\n\n    const itemMetadata = getItemMetadata(\n      'column',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollLeft + width;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < columnCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('column', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  getColumnWidth: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.columnMetadataMap[index].size,\n\n  getEstimatedTotalHeight,\n  getEstimatedTotalWidth,\n\n  getOffsetForColumnAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'column',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getOffsetForRowAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number =>\n    getOffsetForIndexAndAlignment(\n      'row',\n      props,\n      index,\n      align,\n      scrollOffset,\n      instanceProps,\n      scrollbarSize\n    ),\n\n  getRowOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata('row', props, index, instanceProps).offset,\n\n  getRowHeight: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.rowMetadataMap[index].size,\n\n  getRowStartIndexForOffset: (\n    props: Props<any>,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem('row', props, instanceProps, scrollTop),\n\n  getRowStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollTop: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { rowCount, height } = props;\n\n    const itemMetadata = getItemMetadata(\n      'row',\n      props,\n      startIndex,\n      instanceProps\n    );\n    const maxOffset = scrollTop + height;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < rowCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata('row', props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const {\n      estimatedColumnWidth,\n      estimatedRowHeight,\n    } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      columnMetadataMap: {},\n      estimatedColumnWidth: estimatedColumnWidth || DEFAULT_ESTIMATED_ITEM_SIZE,\n      estimatedRowHeight: estimatedRowHeight || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredColumnIndex: -1,\n      lastMeasuredRowIndex: -1,\n      rowMetadataMap: {},\n    };\n\n    instance.resetAfterColumnIndex = (\n      columnIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ columnIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterRowIndex = (\n      rowIndex: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instance.resetAfterIndices({ rowIndex, shouldForceUpdate });\n    };\n\n    instance.resetAfterIndices = ({\n      columnIndex,\n      rowIndex,\n      shouldForceUpdate = true,\n    }: {\n      columnIndex?: number,\n      rowIndex?: number,\n      shouldForceUpdate: boolean,\n    }) => {\n      if (typeof columnIndex === 'number') {\n        instanceProps.lastMeasuredColumnIndex = Math.min(\n          instanceProps.lastMeasuredColumnIndex,\n          columnIndex - 1\n        );\n      }\n      if (typeof rowIndex === 'number') {\n        instanceProps.lastMeasuredRowIndex = Math.min(\n          instanceProps.lastMeasuredRowIndex,\n          rowIndex - 1\n        );\n      }\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'function') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      } else if (typeof rowHeight !== 'function') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeGrid;\n", "// @flow\n\nimport memoizeOne from 'memoize-one';\nimport { createElement, PureComponent } from 'react';\nimport { cancelTimeout, requestTimeout } from './timer';\nimport { getScrollbarSize, getRTLOffsetType } from './domHelpers';\n\nimport type { TimeoutID } from './timer';\n\nexport type ScrollToAlign = 'auto' | 'smart' | 'center' | 'start' | 'end';\n\ntype itemSize = number | ((index: number) => number);\n// TODO Deprecate directions \"horizontal\" and \"vertical\"\ntype Direction = 'ltr' | 'rtl' | 'horizontal' | 'vertical';\ntype Layout = 'horizontal' | 'vertical';\n\ntype RenderComponentProps<T> = {|\n  data: T,\n  index: number,\n  isScrolling?: boolean,\n  style: Object,\n|};\ntype RenderComponent<T> = React$ComponentType<$Shape<RenderComponentProps<T>>>;\n\ntype ScrollDirection = 'forward' | 'backward';\n\ntype onItemsRenderedCallback = ({\n  overscanStartIndex: number,\n  overscanStopIndex: number,\n  visibleStartIndex: number,\n  visibleStopIndex: number,\n}) => void;\ntype onScrollCallback = ({\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n}) => void;\n\ntype ScrollEvent = SyntheticEvent<HTMLDivElement>;\ntype ItemStyleCache = { [index: number]: Object };\n\ntype OuterProps = {|\n  children: React$Node,\n  className: string | void,\n  onScroll: ScrollEvent => void,\n  style: {\n    [string]: mixed,\n  },\n|};\n\ntype InnerProps = {|\n  children: React$Node,\n  style: {\n    [string]: mixed,\n  },\n|};\n\nexport type Props<T> = {|\n  children: RenderComponent<T>,\n  className?: string,\n  direction: Direction,\n  height: number | string,\n  initialScrollOffset?: number,\n  innerRef?: any,\n  innerElementType?: string | React$AbstractComponent<InnerProps, any>,\n  innerTagName?: string, // deprecated\n  itemCount: number,\n  itemData: T,\n  itemKey?: (index: number, data: T) => any,\n  itemSize: itemSize,\n  layout: Layout,\n  onItemsRendered?: onItemsRenderedCallback,\n  onScroll?: onScrollCallback,\n  outerRef?: any,\n  outerElementType?: string | React$AbstractComponent<OuterProps, any>,\n  outerTagName?: string, // deprecated\n  overscanCount: number,\n  style?: Object,\n  useIsScrolling: boolean,\n  width: number | string,\n|};\n\ntype State = {|\n  instance: any,\n  isScrolling: boolean,\n  scrollDirection: ScrollDirection,\n  scrollOffset: number,\n  scrollUpdateWasRequested: boolean,\n|};\n\ntype GetItemOffset = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetItemSize = (\n  props: Props<any>,\n  index: number,\n  instanceProps: any\n) => number;\ntype GetEstimatedTotalSize = (props: Props<any>, instanceProps: any) => number;\ntype GetOffsetForIndexAndAlignment = (\n  props: Props<any>,\n  index: number,\n  align: ScrollToAlign,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype GetStartIndexForOffset = (\n  props: Props<any>,\n  offset: number,\n  instanceProps: any\n) => number;\ntype GetStopIndexForStartIndex = (\n  props: Props<any>,\n  startIndex: number,\n  scrollOffset: number,\n  instanceProps: any\n) => number;\ntype InitInstanceProps = (props: Props<any>, instance: any) => any;\ntype ValidateProps = (props: Props<any>) => void;\n\nconst IS_SCROLLING_DEBOUNCE_INTERVAL = 150;\n\nconst defaultItemKey = (index: number, data: any) => index;\n\n// In DEV mode, this Set helps us only log a warning once per component instance.\n// This avoids spamming the console every time a render happens.\nlet devWarningsDirection = null;\nlet devWarningsTagName = null;\nif (process.env.NODE_ENV !== 'production') {\n  if (typeof window !== 'undefined' && typeof window.WeakSet !== 'undefined') {\n    devWarningsDirection = new WeakSet();\n    devWarningsTagName = new WeakSet();\n  }\n}\n\nexport default function createListComponent({\n  getItemOffset,\n  getEstimatedTotalSize,\n  getItemSize,\n  getOffsetForIndexAndAlignment,\n  getStartIndexForOffset,\n  getStopIndexForStartIndex,\n  initInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange,\n  validateProps,\n}: {|\n  getItemOffset: GetItemOffset,\n  getEstimatedTotalSize: GetEstimatedTotalSize,\n  getItemSize: GetItemSize,\n  getOffsetForIndexAndAlignment: GetOffsetForIndexAndAlignment,\n  getStartIndexForOffset: GetStartIndexForOffset,\n  getStopIndexForStartIndex: GetStopIndexForStartIndex,\n  initInstanceProps: InitInstanceProps,\n  shouldResetStyleCacheOnItemSizeChange: boolean,\n  validateProps: ValidateProps,\n|}) {\n  return class List<T> extends PureComponent<Props<T>, State> {\n    _instanceProps: any = initInstanceProps(this.props, this);\n    _outerRef: ?HTMLDivElement;\n    _resetIsScrollingTimeoutId: TimeoutID | null = null;\n\n    static defaultProps = {\n      direction: 'ltr',\n      itemData: undefined,\n      layout: 'vertical',\n      overscanCount: 2,\n      useIsScrolling: false,\n    };\n\n    state: State = {\n      instance: this,\n      isScrolling: false,\n      scrollDirection: 'forward',\n      scrollOffset:\n        typeof this.props.initialScrollOffset === 'number'\n          ? this.props.initialScrollOffset\n          : 0,\n      scrollUpdateWasRequested: false,\n    };\n\n    // Always use explicit constructor for React components.\n    // It produces less code after transpilation. (#26)\n    // eslint-disable-next-line no-useless-constructor\n    constructor(props: Props<T>) {\n      super(props);\n    }\n\n    static getDerivedStateFromProps(\n      nextProps: Props<T>,\n      prevState: State\n    ): $Shape<State> | null {\n      validateSharedProps(nextProps, prevState);\n      validateProps(nextProps);\n      return null;\n    }\n\n    scrollTo(scrollOffset: number): void {\n      scrollOffset = Math.max(0, scrollOffset);\n\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollOffset) {\n          return null;\n        }\n        return {\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset: scrollOffset,\n          scrollUpdateWasRequested: true,\n        };\n      }, this._resetIsScrollingDebounced);\n    }\n\n    scrollToItem(index: number, align: ScrollToAlign = 'auto'): void {\n      const { itemCount, layout } = this.props;\n      const { scrollOffset } = this.state;\n\n      index = Math.max(0, Math.min(index, itemCount - 1));\n\n      // The scrollbar size should be considered when scrolling an item into view, to ensure it's fully visible.\n      // But we only need to account for its size when it's actually visible.\n      // This is an edge case for lists; normally they only scroll in the dominant direction.\n      let scrollbarSize = 0;\n      if (this._outerRef) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        if (layout === 'vertical') {\n          scrollbarSize =\n            outerRef.scrollWidth > outerRef.clientWidth\n              ? getScrollbarSize()\n              : 0;\n        } else {\n          scrollbarSize =\n            outerRef.scrollHeight > outerRef.clientHeight\n              ? getScrollbarSize()\n              : 0;\n        }\n      }\n\n      this.scrollTo(\n        getOffsetForIndexAndAlignment(\n          this.props,\n          index,\n          align,\n          scrollOffset,\n          this._instanceProps,\n          scrollbarSize\n        )\n      );\n    }\n\n    componentDidMount() {\n      const { direction, initialScrollOffset, layout } = this.props;\n\n      if (typeof initialScrollOffset === 'number' && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          outerRef.scrollLeft = initialScrollOffset;\n        } else {\n          outerRef.scrollTop = initialScrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentDidUpdate() {\n      const { direction, layout } = this.props;\n      const { scrollOffset, scrollUpdateWasRequested } = this.state;\n\n      if (scrollUpdateWasRequested && this._outerRef != null) {\n        const outerRef = ((this._outerRef: any): HTMLElement);\n\n        // TODO Deprecate direction \"horizontal\"\n        if (direction === 'horizontal' || layout === 'horizontal') {\n          if (direction === 'rtl') {\n            // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n            // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n            // So we need to determine which browser behavior we're dealing with, and mimic it.\n            switch (getRTLOffsetType()) {\n              case 'negative':\n                outerRef.scrollLeft = -scrollOffset;\n                break;\n              case 'positive-ascending':\n                outerRef.scrollLeft = scrollOffset;\n                break;\n              default:\n                const { clientWidth, scrollWidth } = outerRef;\n                outerRef.scrollLeft = scrollWidth - clientWidth - scrollOffset;\n                break;\n            }\n          } else {\n            outerRef.scrollLeft = scrollOffset;\n          }\n        } else {\n          outerRef.scrollTop = scrollOffset;\n        }\n      }\n\n      this._callPropsCallbacks();\n    }\n\n    componentWillUnmount() {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n    }\n\n    render() {\n      const {\n        children,\n        className,\n        direction,\n        height,\n        innerRef,\n        innerElementType,\n        innerTagName,\n        itemCount,\n        itemData,\n        itemKey = defaultItemKey,\n        layout,\n        outerElementType,\n        outerTagName,\n        style,\n        useIsScrolling,\n        width,\n      } = this.props;\n      const { isScrolling } = this.state;\n\n      // TODO Deprecate direction \"horizontal\"\n      const isHorizontal =\n        direction === 'horizontal' || layout === 'horizontal';\n\n      const onScroll = isHorizontal\n        ? this._onScrollHorizontal\n        : this._onScrollVertical;\n\n      const [startIndex, stopIndex] = this._getRangeToRender();\n\n      const items = [];\n      if (itemCount > 0) {\n        for (let index = startIndex; index <= stopIndex; index++) {\n          items.push(\n            createElement(children, {\n              data: itemData,\n              key: itemKey(index, itemData),\n              index,\n              isScrolling: useIsScrolling ? isScrolling : undefined,\n              style: this._getItemStyle(index),\n            })\n          );\n        }\n      }\n\n      // Read this value AFTER items have been created,\n      // So their actual sizes (if variable) are taken into consideration.\n      const estimatedTotalSize = getEstimatedTotalSize(\n        this.props,\n        this._instanceProps\n      );\n\n      return createElement(\n        outerElementType || outerTagName || 'div',\n        {\n          className,\n          onScroll,\n          ref: this._outerRefSetter,\n          style: {\n            position: 'relative',\n            height,\n            width,\n            overflow: 'auto',\n            WebkitOverflowScrolling: 'touch',\n            willChange: 'transform',\n            direction,\n            ...style,\n          },\n        },\n        createElement(innerElementType || innerTagName || 'div', {\n          children: items,\n          ref: innerRef,\n          style: {\n            height: isHorizontal ? '100%' : estimatedTotalSize,\n            pointerEvents: isScrolling ? 'none' : undefined,\n            width: isHorizontal ? estimatedTotalSize : '100%',\n          },\n        })\n      );\n    }\n\n    _callOnItemsRendered: (\n      overscanStartIndex: number,\n      overscanStopIndex: number,\n      visibleStartIndex: number,\n      visibleStopIndex: number\n    ) => void;\n    _callOnItemsRendered = memoizeOne(\n      (\n        overscanStartIndex: number,\n        overscanStopIndex: number,\n        visibleStartIndex: number,\n        visibleStopIndex: number\n      ) =>\n        ((this.props.onItemsRendered: any): onItemsRenderedCallback)({\n          overscanStartIndex,\n          overscanStopIndex,\n          visibleStartIndex,\n          visibleStopIndex,\n        })\n    );\n\n    _callOnScroll: (\n      scrollDirection: ScrollDirection,\n      scrollOffset: number,\n      scrollUpdateWasRequested: boolean\n    ) => void;\n    _callOnScroll = memoizeOne(\n      (\n        scrollDirection: ScrollDirection,\n        scrollOffset: number,\n        scrollUpdateWasRequested: boolean\n      ) =>\n        ((this.props.onScroll: any): onScrollCallback)({\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        })\n    );\n\n    _callPropsCallbacks() {\n      if (typeof this.props.onItemsRendered === 'function') {\n        const { itemCount } = this.props;\n        if (itemCount > 0) {\n          const [\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex,\n          ] = this._getRangeToRender();\n          this._callOnItemsRendered(\n            overscanStartIndex,\n            overscanStopIndex,\n            visibleStartIndex,\n            visibleStopIndex\n          );\n        }\n      }\n\n      if (typeof this.props.onScroll === 'function') {\n        const {\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested,\n        } = this.state;\n        this._callOnScroll(\n          scrollDirection,\n          scrollOffset,\n          scrollUpdateWasRequested\n        );\n      }\n    }\n\n    // Lazily create and cache item styles while scrolling,\n    // So that pure component sCU will prevent re-renders.\n    // We maintain this cache, and pass a style prop rather than index,\n    // So that List can clear cached styles and force item re-render if necessary.\n    _getItemStyle: (index: number) => Object;\n    _getItemStyle = (index: number): Object => {\n      const { direction, itemSize, layout } = this.props;\n\n      const itemStyleCache = this._getItemStyleCache(\n        shouldResetStyleCacheOnItemSizeChange && itemSize,\n        shouldResetStyleCacheOnItemSizeChange && layout,\n        shouldResetStyleCacheOnItemSizeChange && direction\n      );\n\n      let style;\n      if (itemStyleCache.hasOwnProperty(index)) {\n        style = itemStyleCache[index];\n      } else {\n        const offset = getItemOffset(this.props, index, this._instanceProps);\n        const size = getItemSize(this.props, index, this._instanceProps);\n\n        // TODO Deprecate direction \"horizontal\"\n        const isHorizontal =\n          direction === 'horizontal' || layout === 'horizontal';\n\n        const isRtl = direction === 'rtl';\n        const offsetHorizontal = isHorizontal ? offset : 0;\n        itemStyleCache[index] = style = {\n          position: 'absolute',\n          left: isRtl ? undefined : offsetHorizontal,\n          right: isRtl ? offsetHorizontal : undefined,\n          top: !isHorizontal ? offset : 0,\n          height: !isHorizontal ? size : '100%',\n          width: isHorizontal ? size : '100%',\n        };\n      }\n\n      return style;\n    };\n\n    _getItemStyleCache: (_: any, __: any, ___: any) => ItemStyleCache;\n    _getItemStyleCache = memoizeOne((_: any, __: any, ___: any) => ({}));\n\n    _getRangeToRender(): [number, number, number, number] {\n      const { itemCount, overscanCount } = this.props;\n      const { isScrolling, scrollDirection, scrollOffset } = this.state;\n\n      if (itemCount === 0) {\n        return [0, 0, 0, 0];\n      }\n\n      const startIndex = getStartIndexForOffset(\n        this.props,\n        scrollOffset,\n        this._instanceProps\n      );\n      const stopIndex = getStopIndexForStartIndex(\n        this.props,\n        startIndex,\n        scrollOffset,\n        this._instanceProps\n      );\n\n      // Overscan by one item in each direction so that tab/focus works.\n      // If there isn't at least one extra item, tab loops back around.\n      const overscanBackward =\n        !isScrolling || scrollDirection === 'backward'\n          ? Math.max(1, overscanCount)\n          : 1;\n      const overscanForward =\n        !isScrolling || scrollDirection === 'forward'\n          ? Math.max(1, overscanCount)\n          : 1;\n\n      return [\n        Math.max(0, startIndex - overscanBackward),\n        Math.max(0, Math.min(itemCount - 1, stopIndex + overscanForward)),\n        startIndex,\n        stopIndex,\n      ];\n    }\n\n    _onScrollHorizontal = (event: ScrollEvent): void => {\n      const { clientWidth, scrollLeft, scrollWidth } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollLeft) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        const { direction } = this.props;\n\n        let scrollOffset = scrollLeft;\n        if (direction === 'rtl') {\n          // TRICKY According to the spec, scrollLeft should be negative for RTL aligned elements.\n          // This is not the case for all browsers though (e.g. Chrome reports values as positive, measured relative to the left).\n          // It's also easier for this component if we convert offsets to the same format as they would be in for ltr.\n          // So the simplest solution is to determine which browser behavior we're dealing with, and convert based on it.\n          switch (getRTLOffsetType()) {\n            case 'negative':\n              scrollOffset = -scrollLeft;\n              break;\n            case 'positive-descending':\n              scrollOffset = scrollWidth - clientWidth - scrollLeft;\n              break;\n          }\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        scrollOffset = Math.max(\n          0,\n          Math.min(scrollOffset, scrollWidth - clientWidth)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _onScrollVertical = (event: ScrollEvent): void => {\n      const { clientHeight, scrollHeight, scrollTop } = event.currentTarget;\n      this.setState(prevState => {\n        if (prevState.scrollOffset === scrollTop) {\n          // Scroll position may have been updated by cDM/cDU,\n          // In which case we don't need to trigger another render,\n          // And we don't want to update state.isScrolling.\n          return null;\n        }\n\n        // Prevent Safari's elastic scrolling from causing visual shaking when scrolling past bounds.\n        const scrollOffset = Math.max(\n          0,\n          Math.min(scrollTop, scrollHeight - clientHeight)\n        );\n\n        return {\n          isScrolling: true,\n          scrollDirection:\n            prevState.scrollOffset < scrollOffset ? 'forward' : 'backward',\n          scrollOffset,\n          scrollUpdateWasRequested: false,\n        };\n      }, this._resetIsScrollingDebounced);\n    };\n\n    _outerRefSetter = (ref: any): void => {\n      const { outerRef } = this.props;\n\n      this._outerRef = ((ref: any): HTMLDivElement);\n\n      if (typeof outerRef === 'function') {\n        outerRef(ref);\n      } else if (\n        outerRef != null &&\n        typeof outerRef === 'object' &&\n        outerRef.hasOwnProperty('current')\n      ) {\n        outerRef.current = ref;\n      }\n    };\n\n    _resetIsScrollingDebounced = () => {\n      if (this._resetIsScrollingTimeoutId !== null) {\n        cancelTimeout(this._resetIsScrollingTimeoutId);\n      }\n\n      this._resetIsScrollingTimeoutId = requestTimeout(\n        this._resetIsScrolling,\n        IS_SCROLLING_DEBOUNCE_INTERVAL\n      );\n    };\n\n    _resetIsScrolling = () => {\n      this._resetIsScrollingTimeoutId = null;\n\n      this.setState({ isScrolling: false }, () => {\n        // Clear style cache after state update has been committed.\n        // This way we don't break pure sCU for items that don't use isScrolling param.\n        this._getItemStyleCache(-1, null);\n      });\n    };\n  };\n}\n\n// NOTE: I considered further wrapping individual items with a pure ListItem component.\n// This would avoid ever calling the render function for the same index more than once,\n// But it would also add the overhead of a lot of components/fibers.\n// I assume people already do this (render function returning a class component),\n// So my doing it would just unnecessarily double the wrappers.\n\nconst validateSharedProps = (\n  {\n    children,\n    direction,\n    height,\n    layout,\n    innerTagName,\n    outerTagName,\n    width,\n  }: Props<any>,\n  { instance }: State\n): void => {\n  if (process.env.NODE_ENV !== 'production') {\n    if (innerTagName != null || outerTagName != null) {\n      if (devWarningsTagName && !devWarningsTagName.has(instance)) {\n        devWarningsTagName.add(instance);\n        console.warn(\n          'The innerTagName and outerTagName props have been deprecated. ' +\n            'Please use the innerElementType and outerElementType props instead.'\n        );\n      }\n    }\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n\n    switch (direction) {\n      case 'horizontal':\n      case 'vertical':\n        if (devWarningsDirection && !devWarningsDirection.has(instance)) {\n          devWarningsDirection.add(instance);\n          console.warn(\n            'The direction prop should be either \"ltr\" (default) or \"rtl\". ' +\n              'Please use the layout prop to specify \"vertical\" (default) or \"horizontal\" orientation.'\n          );\n        }\n        break;\n      case 'ltr':\n      case 'rtl':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"direction\" prop has been specified. ' +\n            'Value should be either \"ltr\" or \"rtl\". ' +\n            `\"${direction}\" was specified.`\n        );\n    }\n\n    switch (layout) {\n      case 'horizontal':\n      case 'vertical':\n        // Valid values\n        break;\n      default:\n        throw Error(\n          'An invalid \"layout\" prop has been specified. ' +\n            'Value should be either \"horizontal\" or \"vertical\". ' +\n            `\"${layout}\" was specified.`\n        );\n    }\n\n    if (children == null) {\n      throw Error(\n        'An invalid \"children\" prop has been specified. ' +\n          'Value should be a React component. ' +\n          `\"${children === null ? 'null' : typeof children}\" was specified.`\n      );\n    }\n\n    if (isHorizontal && typeof width !== 'number') {\n      throw Error(\n        'An invalid \"width\" prop has been specified. ' +\n          'Horizontal lists must specify a number for width. ' +\n          `\"${width === null ? 'null' : typeof width}\" was specified.`\n      );\n    } else if (!isHorizontal && typeof height !== 'number') {\n      throw Error(\n        'An invalid \"height\" prop has been specified. ' +\n          'Vertical lists must specify a number for height. ' +\n          `\"${height === null ? 'null' : typeof height}\" was specified.`\n      );\n    }\n  }\n};\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createListComponent';\n\nconst DEFAULT_ESTIMATED_ITEM_SIZE = 50;\n\ntype VariableSizeProps = {|\n  estimatedItemSize: number,\n  ...Props<any>,\n|};\n\ntype itemSizeGetter = (index: number) => number;\n\ntype ItemMetadata = {|\n  offset: number,\n  size: number,\n|};\ntype InstanceProps = {|\n  itemMetadataMap: { [index: number]: ItemMetadata },\n  estimatedItemSize: number,\n  lastMeasuredIndex: number,\n|};\n\nconst getItemMetadata = (\n  props: Props<any>,\n  index: number,\n  instanceProps: InstanceProps\n): ItemMetadata => {\n  const { itemSize } = ((props: any): VariableSizeProps);\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  if (index > lastMeasuredIndex) {\n    let offset = 0;\n    if (lastMeasuredIndex >= 0) {\n      const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n      offset = itemMetadata.offset + itemMetadata.size;\n    }\n\n    for (let i = lastMeasuredIndex + 1; i <= index; i++) {\n      let size = ((itemSize: any): itemSizeGetter)(i);\n\n      itemMetadataMap[i] = {\n        offset,\n        size,\n      };\n\n      offset += size;\n    }\n\n    instanceProps.lastMeasuredIndex = index;\n  }\n\n  return itemMetadataMap[index];\n};\n\nconst findNearestItem = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  offset: number\n) => {\n  const { itemMetadataMap, lastMeasuredIndex } = instanceProps;\n\n  const lastMeasuredItemOffset =\n    lastMeasuredIndex > 0 ? itemMetadataMap[lastMeasuredIndex].offset : 0;\n\n  if (lastMeasuredItemOffset >= offset) {\n    // If we've already measured items within this range just use a binary search as it's faster.\n    return findNearestItemBinarySearch(\n      props,\n      instanceProps,\n      lastMeasuredIndex,\n      0,\n      offset\n    );\n  } else {\n    // If we haven't yet measured this high, fallback to an exponential search with an inner binary search.\n    // The exponential search avoids pre-computing sizes for the full set of items as a binary search would.\n    // The overall complexity for this approach is O(log n).\n    return findNearestItemExponentialSearch(\n      props,\n      instanceProps,\n      Math.max(0, lastMeasuredIndex),\n      offset\n    );\n  }\n};\n\nconst findNearestItemBinarySearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  high: number,\n  low: number,\n  offset: number\n): number => {\n  while (low <= high) {\n    const middle = low + Math.floor((high - low) / 2);\n    const currentOffset = getItemMetadata(props, middle, instanceProps).offset;\n\n    if (currentOffset === offset) {\n      return middle;\n    } else if (currentOffset < offset) {\n      low = middle + 1;\n    } else if (currentOffset > offset) {\n      high = middle - 1;\n    }\n  }\n\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\n\nconst findNearestItemExponentialSearch = (\n  props: Props<any>,\n  instanceProps: InstanceProps,\n  index: number,\n  offset: number\n): number => {\n  const { itemCount } = props;\n  let interval = 1;\n\n  while (\n    index < itemCount &&\n    getItemMetadata(props, index, instanceProps).offset < offset\n  ) {\n    index += interval;\n    interval *= 2;\n  }\n\n  return findNearestItemBinarySearch(\n    props,\n    instanceProps,\n    Math.min(index, itemCount - 1),\n    Math.floor(index / 2),\n    offset\n  );\n};\n\nconst getEstimatedTotalSize = (\n  { itemCount }: Props<any>,\n  { itemMetadataMap, estimatedItemSize, lastMeasuredIndex }: InstanceProps\n) => {\n  let totalSizeOfMeasuredItems = 0;\n\n  // Edge case check for when the number of items decreases while a scroll is in progress.\n  // https://github.com/bvaughn/react-window/pull/138\n  if (lastMeasuredIndex >= itemCount) {\n    lastMeasuredIndex = itemCount - 1;\n  }\n\n  if (lastMeasuredIndex >= 0) {\n    const itemMetadata = itemMetadataMap[lastMeasuredIndex];\n    totalSizeOfMeasuredItems = itemMetadata.offset + itemMetadata.size;\n  }\n\n  const numUnmeasuredItems = itemCount - lastMeasuredIndex - 1;\n  const totalSizeOfUnmeasuredItems = numUnmeasuredItems * estimatedItemSize;\n\n  return totalSizeOfMeasuredItems + totalSizeOfUnmeasuredItems;\n};\n\nconst VariableSizeList = createListComponent({\n  getItemOffset: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => getItemMetadata(props, index, instanceProps).offset,\n\n  getItemSize: (\n    props: Props<any>,\n    index: number,\n    instanceProps: InstanceProps\n  ): number => instanceProps.itemMetadataMap[index].size,\n\n  getEstimatedTotalSize,\n\n  getOffsetForIndexAndAlignment: (\n    props: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    const { direction, height, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, index, instanceProps);\n\n    // Get estimated total size after ItemMetadata is computed,\n    // To ensure it reflects actual measurements instead of just estimates.\n    const estimatedTotalSize = getEstimatedTotalSize(props, instanceProps);\n\n    const maxOffset = Math.max(\n      0,\n      Math.min(estimatedTotalSize - size, itemMetadata.offset)\n    );\n    const minOffset = Math.max(\n      0,\n      itemMetadata.offset - size + itemMetadata.size + scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        return Math.round(minOffset + (maxOffset - minOffset) / 2);\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    props: Props<any>,\n    offset: number,\n    instanceProps: InstanceProps\n  ): number => findNearestItem(props, instanceProps, offset),\n\n  getStopIndexForStartIndex: (\n    props: Props<any>,\n    startIndex: number,\n    scrollOffset: number,\n    instanceProps: InstanceProps\n  ): number => {\n    const { direction, height, itemCount, layout, width } = props;\n\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const itemMetadata = getItemMetadata(props, startIndex, instanceProps);\n    const maxOffset = scrollOffset + size;\n\n    let offset = itemMetadata.offset + itemMetadata.size;\n    let stopIndex = startIndex;\n\n    while (stopIndex < itemCount - 1 && offset < maxOffset) {\n      stopIndex++;\n      offset += getItemMetadata(props, stopIndex, instanceProps).size;\n    }\n\n    return stopIndex;\n  },\n\n  initInstanceProps(props: Props<any>, instance: any): InstanceProps {\n    const { estimatedItemSize } = ((props: any): VariableSizeProps);\n\n    const instanceProps = {\n      itemMetadataMap: {},\n      estimatedItemSize: estimatedItemSize || DEFAULT_ESTIMATED_ITEM_SIZE,\n      lastMeasuredIndex: -1,\n    };\n\n    instance.resetAfterIndex = (\n      index: number,\n      shouldForceUpdate?: boolean = true\n    ) => {\n      instanceProps.lastMeasuredIndex = Math.min(\n        instanceProps.lastMeasuredIndex,\n        index - 1\n      );\n\n      // We could potentially optimize further by only evicting styles after this index,\n      // But since styles are only cached while scrolling is in progress-\n      // It seems an unnecessary optimization.\n      // It's unlikely that resetAfterIndex() will be called while a user is scrolling.\n      instance._getItemStyleCache(-1);\n\n      if (shouldForceUpdate) {\n        instance.forceUpdate();\n      }\n    };\n\n    return instanceProps;\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: false,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'function') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a function. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default VariableSizeList;\n", "// @flow\n\nimport createGridComponent from './createGridComponent';\n\nimport type { Pro<PERSON>, ScrollToAlign } from './createGridComponent';\n\nconst FixedSizeGrid = createGridComponent({\n  getColumnOffset: ({ columnWidth }: Props<any>, index: number): number =>\n    index * ((columnWidth: any): number),\n\n  getColumnWidth: ({ columnWidth }: Props<any>, index: number): number =>\n    ((columnWidth: any): number),\n\n  getRowOffset: ({ rowHeight }: Props<any>, index: number): number =>\n    index * ((rowHeight: any): number),\n\n  getRowHeight: ({ rowHeight }: Props<any>, index: number): number =>\n    ((rowHeight: any): number),\n\n  getEstimatedTotalHeight: ({ rowCount, rowHeight }: Props<any>) =>\n    ((rowHeight: any): number) * rowCount,\n\n  getEstimatedTotalWidth: ({ columnCount, columnWidth }: Props<any>) =>\n    ((columnWidth: any): number) * columnCount,\n\n  getOffsetForColumnAndAlignment: (\n    { columnCount, columnWidth, width }: Props<any>,\n    columnIndex: number,\n    align: ScrollToAlign,\n    scrollLeft: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastColumnOffset = Math.max(\n      0,\n      columnCount * ((columnWidth: any): number) - width\n    );\n    const maxOffset = Math.min(\n      lastColumnOffset,\n      columnIndex * ((columnWidth: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      columnIndex * ((columnWidth: any): number) -\n        width +\n        scrollbarSize +\n        ((columnWidth: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollLeft >= minOffset - width && scrollLeft <= maxOffset + width) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(width / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastColumnOffset + Math.floor(width / 2)) {\n          return lastColumnOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollLeft >= minOffset && scrollLeft <= maxOffset) {\n          return scrollLeft;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollLeft < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getOffsetForRowAndAlignment: (\n    { rowHeight, height, rowCount }: Props<any>,\n    rowIndex: number,\n    align: ScrollToAlign,\n    scrollTop: number,\n    instanceProps: typeof undefined,\n    scrollbarSize: number\n  ): number => {\n    const lastRowOffset = Math.max(\n      0,\n      rowCount * ((rowHeight: any): number) - height\n    );\n    const maxOffset = Math.min(\n      lastRowOffset,\n      rowIndex * ((rowHeight: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      rowIndex * ((rowHeight: any): number) -\n        height +\n        scrollbarSize +\n        ((rowHeight: any): number)\n    );\n\n    if (align === 'smart') {\n      if (scrollTop >= minOffset - height && scrollTop <= maxOffset + height) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center':\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(height / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastRowOffset + Math.floor(height / 2)) {\n          return lastRowOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      case 'auto':\n      default:\n        if (scrollTop >= minOffset && scrollTop <= maxOffset) {\n          return scrollTop;\n        } else if (minOffset > maxOffset) {\n          // Because we only take into account the scrollbar size when calculating minOffset\n          // this value can be larger than maxOffset when at the end of the list\n          return minOffset;\n        } else if (scrollTop < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getColumnStartIndexForOffset: (\n    { columnWidth, columnCount }: Props<any>,\n    scrollLeft: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        Math.floor(scrollLeft / ((columnWidth: any): number))\n      )\n    ),\n\n  getColumnStopIndexForStartIndex: (\n    { columnWidth, columnCount, width }: Props<any>,\n    startIndex: number,\n    scrollLeft: number\n  ): number => {\n    const left = startIndex * ((columnWidth: any): number);\n    const numVisibleColumns = Math.ceil(\n      (width + scrollLeft - left) / ((columnWidth: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        columnCount - 1,\n        startIndex + numVisibleColumns - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  getRowStartIndexForOffset: (\n    { rowHeight, rowCount }: Props<any>,\n    scrollTop: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(rowCount - 1, Math.floor(scrollTop / ((rowHeight: any): number)))\n    ),\n\n  getRowStopIndexForStartIndex: (\n    { rowHeight, rowCount, height }: Props<any>,\n    startIndex: number,\n    scrollTop: number\n  ): number => {\n    const top = startIndex * ((rowHeight: any): number);\n    const numVisibleRows = Math.ceil(\n      (height + scrollTop - top) / ((rowHeight: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        rowCount - 1,\n        startIndex + numVisibleRows - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ columnWidth, rowHeight }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof columnWidth !== 'number') {\n        throw Error(\n          'An invalid \"columnWidth\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${\n              columnWidth === null ? 'null' : typeof columnWidth\n            }\" was specified.`\n        );\n      }\n\n      if (typeof rowHeight !== 'number') {\n        throw Error(\n          'An invalid \"rowHeight\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${rowHeight === null ? 'null' : typeof rowHeight}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeGrid;\n", "// @flow\n\nimport createListComponent from './createListComponent';\n\nimport type { Props, ScrollToAlign } from './createListComponent';\n\ntype InstanceProps = any;\n\nconst FixedSizeList = createListComponent({\n  getItemOffset: ({ itemSize }: Props<any>, index: number): number =>\n    index * ((itemSize: any): number),\n\n  getItemSize: ({ itemSize }: Props<any>, index: number): number =>\n    ((itemSize: any): number),\n\n  getEstimatedTotalSize: ({ itemCount, itemSize }: Props<any>) =>\n    ((itemSize: any): number) * itemCount,\n\n  getOffsetForIndexAndAlignment: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    index: number,\n    align: ScrollToAlign,\n    scrollOffset: number,\n    instanceProps: InstanceProps,\n    scrollbarSize: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const size = (((isHorizontal ? width : height): any): number);\n    const lastItemOffset = Math.max(\n      0,\n      itemCount * ((itemSize: any): number) - size\n    );\n    const maxOffset = Math.min(\n      lastItemOffset,\n      index * ((itemSize: any): number)\n    );\n    const minOffset = Math.max(\n      0,\n      index * ((itemSize: any): number) -\n        size +\n        ((itemSize: any): number) +\n        scrollbarSize\n    );\n\n    if (align === 'smart') {\n      if (\n        scrollOffset >= minOffset - size &&\n        scrollOffset <= maxOffset + size\n      ) {\n        align = 'auto';\n      } else {\n        align = 'center';\n      }\n    }\n\n    switch (align) {\n      case 'start':\n        return maxOffset;\n      case 'end':\n        return minOffset;\n      case 'center': {\n        // \"Centered\" offset is usually the average of the min and max.\n        // But near the edges of the list, this doesn't hold true.\n        const middleOffset = Math.round(\n          minOffset + (maxOffset - minOffset) / 2\n        );\n        if (middleOffset < Math.ceil(size / 2)) {\n          return 0; // near the beginning\n        } else if (middleOffset > lastItemOffset + Math.floor(size / 2)) {\n          return lastItemOffset; // near the end\n        } else {\n          return middleOffset;\n        }\n      }\n      case 'auto':\n      default:\n        if (scrollOffset >= minOffset && scrollOffset <= maxOffset) {\n          return scrollOffset;\n        } else if (scrollOffset < minOffset) {\n          return minOffset;\n        } else {\n          return maxOffset;\n        }\n    }\n  },\n\n  getStartIndexForOffset: (\n    { itemCount, itemSize }: Props<any>,\n    offset: number\n  ): number =>\n    Math.max(\n      0,\n      Math.min(itemCount - 1, Math.floor(offset / ((itemSize: any): number)))\n    ),\n\n  getStopIndexForStartIndex: (\n    { direction, height, itemCount, itemSize, layout, width }: Props<any>,\n    startIndex: number,\n    scrollOffset: number\n  ): number => {\n    // TODO Deprecate direction \"horizontal\"\n    const isHorizontal = direction === 'horizontal' || layout === 'horizontal';\n    const offset = startIndex * ((itemSize: any): number);\n    const size = (((isHorizontal ? width : height): any): number);\n    const numVisibleItems = Math.ceil(\n      (size + scrollOffset - offset) / ((itemSize: any): number)\n    );\n    return Math.max(\n      0,\n      Math.min(\n        itemCount - 1,\n        startIndex + numVisibleItems - 1 // -1 is because stop index is inclusive\n      )\n    );\n  },\n\n  initInstanceProps(props: Props<any>): any {\n    // Noop\n  },\n\n  shouldResetStyleCacheOnItemSizeChange: true,\n\n  validateProps: ({ itemSize }: Props<any>): void => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof itemSize !== 'number') {\n        throw Error(\n          'An invalid \"itemSize\" prop has been specified. ' +\n            'Value should be a number. ' +\n            `\"${itemSize === null ? 'null' : typeof itemSize}\" was specified.`\n        );\n      }\n    }\n  },\n});\n\nexport default FixedSizeList;\n", "// @flow\n\n// Pulled from react-compat\n// https://github.com/developit/preact-compat/blob/7c5de00e7c85e2ffd011bf3af02899b63f699d3a/src/index.js#L349\nexport default function shallowDiffers(prev: Object, next: Object): boolean {\n  for (let attribute in prev) {\n    if (!(attribute in next)) {\n      return true;\n    }\n  }\n  for (let attribute in next) {\n    if (prev[attribute] !== next[attribute]) {\n      return true;\n    }\n  }\n  return false;\n}\n", "// @flow\n\nimport shallowDiffers from './shallowDiffers';\n\n// Custom comparison function for React.memo().\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-api.html#reactmemo\nexport default function areEqual(\n  prevProps: Object,\n  nextProps: Object\n): boolean {\n  const { style: prevStyle, ...prevRest } = prevProps;\n  const { style: nextStyle, ...nextRest } = nextProps;\n\n  return (\n    !shallowDiffers(prevStyle, nextStyle) && !shallowDiffers(prevRest, nextRest)\n  );\n}\n", "// @flow\n\nimport areEqual from './areEqual';\nimport shallowDiffers from './shallowDiffers';\n\n// Custom shouldComponentUpdate for class components.\n// It knows to compare individual style props and ignore the wrapper object.\n// See https://reactjs.org/docs/react-component.html#shouldcomponentupdate\nexport default function shouldComponentUpdate(\n  nextProps: Object,\n  nextState: Object\n): boolean {\n  return (\n    !areEqual(this.props, nextProps) || shallowDiffers(this.state, nextState)\n  );\n}\n"], "names": ["hasNativePerformanceNow", "performance", "now", "Date", "cancelTimeout", "timeoutID", "cancelAnimationFrame", "id", "requestTimeout", "callback", "delay", "start", "tick", "call", "requestAnimationFrame", "size", "getScrollbarSize", "recalculate", "div", "document", "createElement", "style", "width", "height", "overflow", "body", "append<PERSON><PERSON><PERSON>", "offsetWidth", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "cachedRTLResult", "getRTLOffsetType", "outerDiv", "outerStyle", "direction", "innerDiv", "innerStyle", "scrollLeft", "IS_SCROLLING_DEBOUNCE_INTERVAL", "defaultItemKey", "columnIndex", "data", "rowIndex", "devWarningsOverscanCount", "devWarningsOverscanRowsColumnsCount", "devWarningsTagName", "process", "env", "NODE_ENV", "window", "WeakSet", "createGridComponent", "getColumnOffset", "getColumnStartIndexForOffset", "getColumnStopIndexForStartIndex", "getColumnWidth", "getEstimatedTotalHeight", "getEstimatedTotalWidth", "getOffsetForColumnAndAlignment", "getOffsetForRowAndAlignment", "getRowHeight", "getRowOffset", "getRowStartIndexForOffset", "getRowStopIndexForStartIndex", "initInstanceProps", "shouldResetStyleCacheOnItemSizeChange", "validateProps", "props", "_instanceProps", "_resetIsScrollingTimeoutId", "_outerRef", "state", "instance", "isScrolling", "horizontalScrollDirection", "initialScrollLeft", "scrollTop", "initialScrollTop", "scrollUpdateWasRequested", "verticalScrollDirection", "_callOnItemsRendered", "memoizeOne", "overscanColumnStartIndex", "overscanColumnStopIndex", "overscanRowStartIndex", "overscanRowStopIndex", "visibleColumnStartIndex", "visibleColumnStopIndex", "visibleRowStartIndex", "visibleRowStopIndex", "onItemsRendered", "_callOnScroll", "onScroll", "_getItemStyle", "columnWidth", "rowHeight", "itemStyleCache", "_getItemStyleCache", "key", "hasOwnProperty", "offset", "isRtl", "position", "left", "undefined", "right", "top", "_", "__", "___", "_onScroll", "event", "currentTarget", "clientHeight", "scrollHeight", "scrollWidth", "setState", "prevState", "calculatedScrollLeft", "Math", "max", "min", "calculatedScrollTop", "_resetIsScrollingDebounced", "_outerRefSetter", "ref", "outerRef", "current", "_resetIsScrolling", "getDerivedStateFromProps", "nextProps", "validateSharedProps", "scrollTo", "scrollToItem", "align", "columnCount", "rowCount", "scrollbarSize", "estimatedTotalHeight", "estimatedTotalWidth", "horizontalScrollbarSize", "verticalScrollbarSize", "componentDidMount", "_callPropsCallbacks", "componentDidUpdate", "componentWillUnmount", "render", "children", "className", "innerRef", "innerElementType", "innerTagName", "itemData", "itemKey", "outerElementType", "outerTagName", "useIsScrolling", "_getHorizontalRangeToRender", "columnStartIndex", "columnStopIndex", "_getVerticalRangeToRender", "rowStartIndex", "rowStopIndex", "items", "push", "WebkitOverflowScrolling", "<PERSON><PERSON><PERSON><PERSON>", "pointerEvents", "overscanColumnCount", "overscanColumnsCount", "overscanCount", "overscanCountResolved", "startIndex", "stopIndex", "overscanBackward", "overscanForward", "overscanRowCount", "overscanRowsCount", "PureComponent", "defaultProps", "has", "add", "console", "warn", "Error", "DEFAULT_ESTIMATED_ITEM_SIZE", "rowMetadataMap", "estimatedRowHeight", "lastMeasuredRowIndex", "totalSizeOfMeasuredRows", "itemMetadata", "numUnmeasuredItems", "totalSizeOfUnmeasuredItems", "columnMetadataMap", "estimatedColumnWidth", "lastMeasuredColumnIndex", "getItemMetadata", "itemType", "index", "instanceProps", "itemMetadataMap", "itemSize", "lastMeasuredIndex", "i", "findNearestItem", "lastMeasuredItemOffset", "findNearestItemBinarySearch", "findNearestItemExponentialSearch", "high", "low", "middle", "floor", "currentOffset", "itemCount", "interval", "getOffsetForIndexAndAlignment", "scrollOffset", "estimatedTotalSize", "maxOffset", "minOffset", "round", "VariableSizeGrid", "resetAfterColumnIndex", "shouldForceUpdate", "resetAfterIndices", "resetAfterRowIndex", "forceUpdate", "devWarningsDirection", "createListComponent", "getItemOffset", "getEstimatedTotalSize", "getItemSize", "getStartIndexForOffset", "getStopIndexForStartIndex", "scrollDirection", "initialScrollOffset", "overscanStartIndex", "overscanStopIndex", "visibleStartIndex", "visibleStopIndex", "layout", "isHorizontal", "offsetHorizontal", "_onScrollHorizontal", "_onScrollVertical", "_getRangeToRender", "estimatedItemSize", "totalSizeOfMeasuredItems", "VariableSizeList", "resetAfterIndex", "FixedSizeGrid", "lastColumnOffset", "middleOffset", "ceil", "lastRowOffset", "numVisibleColumns", "numVisibleRows", "FixedSizeList", "lastItemOffset", "numVisibleItems", "shallow<PERSON>iffers", "prev", "next", "attribute", "areEqual", "prevProps", "prevStyle", "prevRest", "nextStyle", "nextRest", "shouldComponentUpdate", "nextState"], "mappings": ";;;;;;;AAEA;AACA;AAEA,IAAMA,uBAAuB,GAC3B,OAAOC,WAAP,KAAuB,QAAvB,IAAmC,OAAOA,WAAW,CAACC,GAAnB,KAA2B,UADhE;AAGA,IAAMA,GAAG,GAAGF,uBAAuB,GAC/B;AAAA,SAAMC,WAAW,CAACC,GAAZ,EAAN;AAAA,CAD+B,GAE/B;AAAA,SAAMC,IAAI,CAACD,GAAL,EAAN;AAAA,CAFJ;AAQO,SAASE,aAAT,CAAuBC,SAAvB,EAA6C;AAClDC,EAAAA,oBAAoB,CAACD,SAAS,CAACE,EAAX,CAApB;AACD;AAEM,SAASC,cAAT,CAAwBC,QAAxB,EAA4CC,KAA5C,EAAsE;AAC3E,MAAMC,KAAK,GAAGT,GAAG,EAAjB;;AAEA,WAASU,IAAT,GAAgB;AACd,QAAIV,GAAG,KAAKS,KAAR,IAAiBD,KAArB,EAA4B;AAC1BD,MAAAA,QAAQ,CAACI,IAAT,CAAc,IAAd;AACD,KAFD,MAEO;AACLR,MAAAA,SAAS,CAACE,EAAV,GAAeO,qBAAqB,CAACF,IAAD,CAApC;AACD;AACF;;AAED,MAAMP,SAAoB,GAAG;AAC3BE,IAAAA,EAAE,EAAEO,qBAAqB,CAACF,IAAD;AADE,GAA7B;AAIA,SAAOP,SAAP;AACD;;AClCD,IAAIU,IAAY,GAAG,CAAC,CAApB;;AAGA,AAAO,SAASC,gBAAT,CAA0BC,WAA1B,EAAiE;AAAA,MAAvCA,WAAuC;AAAvCA,IAAAA,WAAuC,GAAf,KAAe;AAAA;;AACtE,MAAIF,IAAI,KAAK,CAAC,CAAV,IAAeE,WAAnB,EAAgC;AAC9B,QAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAZ;AACA,QAAMC,KAAK,GAAGH,GAAG,CAACG,KAAlB;AACAA,IAAAA,KAAK,CAACC,KAAN,GAAc,MAAd;AACAD,IAAAA,KAAK,CAACE,MAAN,GAAe,MAAf;AACAF,IAAAA,KAAK,CAACG,QAAN,GAAiB,QAAjB;AAEEL,IAAAA,QAAQ,CAACM,IAAX,CAAwCC,WAAxC,CAAoDR,GAApD;AAEAH,IAAAA,IAAI,GAAGG,GAAG,CAACS,WAAJ,GAAkBT,GAAG,CAACU,WAA7B;AAEET,IAAAA,QAAQ,CAACM,IAAX,CAAwCI,WAAxC,CAAoDX,GAApD;AACD;;AAED,SAAOH,IAAP;AACD;AAOD,IAAIe,eAAqC,GAAG,IAA5C;AAGA;AACA;AACA;AACA;AACA;;AACA,AAAO,SAASC,gBAAT,CAA0Bd,WAA1B,EAAwE;AAAA,MAA9CA,WAA8C;AAA9CA,IAAAA,WAA8C,GAAtB,KAAsB;AAAA;;AAC7E,MAAIa,eAAe,KAAK,IAApB,IAA4Bb,WAAhC,EAA6C;AAC3C,QAAMe,QAAQ,GAAGb,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;AACA,QAAMa,UAAU,GAAGD,QAAQ,CAACX,KAA5B;AACAY,IAAAA,UAAU,CAACX,KAAX,GAAmB,MAAnB;AACAW,IAAAA,UAAU,CAACV,MAAX,GAAoB,MAApB;AACAU,IAAAA,UAAU,CAACT,QAAX,GAAsB,QAAtB;AACAS,IAAAA,UAAU,CAACC,SAAX,GAAuB,KAAvB;AAEA,QAAMC,QAAQ,GAAGhB,QAAQ,CAACC,aAAT,CAAuB,KAAvB,CAAjB;AACA,QAAMgB,UAAU,GAAGD,QAAQ,CAACd,KAA5B;AACAe,IAAAA,UAAU,CAACd,KAAX,GAAmB,OAAnB;AACAc,IAAAA,UAAU,CAACb,MAAX,GAAoB,OAApB;AAEAS,IAAAA,QAAQ,CAACN,WAAT,CAAqBS,QAArB;AAEEhB,IAAAA,QAAQ,CAACM,IAAX,CAAwCC,WAAxC,CAAoDM,QAApD;;AAEA,QAAIA,QAAQ,CAACK,UAAT,GAAsB,CAA1B,EAA6B;AAC3BP,MAAAA,eAAe,GAAG,qBAAlB;AACD,KAFD,MAEO;AACLE,MAAAA,QAAQ,CAACK,UAAT,GAAsB,CAAtB;;AACA,UAAIL,QAAQ,CAACK,UAAT,KAAwB,CAA5B,EAA+B;AAC7BP,QAAAA,eAAe,GAAG,UAAlB;AACD,OAFD,MAEO;AACLA,QAAAA,eAAe,GAAG,oBAAlB;AACD;AACF;;AAECX,IAAAA,QAAQ,CAACM,IAAX,CAAwCI,WAAxC,CAAoDG,QAApD;AAEA,WAAOF,eAAP;AACD;;AAED,SAAOA,eAAP;AACD;;ACuED,IAAMQ,8BAA8B,GAAG,GAAvC;;AAEA,IAAMC,cAAc,GAAG,SAAjBA,cAAiB;AAAA,MAAGC,WAAH,QAAGA,WAAH;AAAA,MAAgBC,IAAhB,QAAgBA,IAAhB;AAAA,MAAsBC,QAAtB,QAAsBA,QAAtB;AAAA,SAClBA,QADkB,SACNF,WADM;AAAA,CAAvB;AAIA;;;AACA,IAAIG,wBAAwB,GAAG,IAA/B;AACA,IAAIC,mCAAmC,GAAG,IAA1C;AACA,IAAIC,kBAAkB,GAAG,IAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,MAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,MAAM,CAACC,OAAd,KAA0B,WAA/D,EAA4E;AAC1EP,IAAAA,wBAAwB,gBAAG,IAAIO,OAAJ,EAA3B;AACAN,IAAAA,mCAAmC,gBAAG,IAAIM,OAAJ,EAAtC;AACAL,IAAAA,kBAAkB,gBAAG,IAAIK,OAAJ,EAArB;AACD;AACF;;AAED,AAAe,SAASC,mBAAT,QAgCX;AAAA;;AAAA,MA/BFC,eA+BE,SA/BFA,eA+BE;AAAA,MA9BFC,4BA8BE,SA9BFA,4BA8BE;AAAA,MA7BFC,+BA6BE,SA7BFA,+BA6BE;AAAA,MA5BFC,cA4BE,SA5BFA,cA4BE;AAAA,MA3BFC,uBA2BE,SA3BFA,uBA2BE;AAAA,MA1BFC,sBA0BE,SA1BFA,sBA0BE;AAAA,MAzBFC,8BAyBE,SAzBFA,8BAyBE;AAAA,MAxBFC,2BAwBE,SAxBFA,2BAwBE;AAAA,MAvBFC,YAuBE,SAvBFA,YAuBE;AAAA,MAtBFC,YAsBE,SAtBFA,YAsBE;AAAA,MArBFC,yBAqBE,SArBFA,yBAqBE;AAAA,MApBFC,4BAoBE,SApBFA,4BAoBE;AAAA,MAnBFC,iBAmBE,SAnBFA,iBAmBE;AAAA,MAlBFC,qCAkBE,SAlBFA,qCAkBE;AAAA,MAjBFC,aAiBE,SAjBFA,aAiBE;AACF;AAAA;;AA2BE;AACA;AACA;AACA,kBAAYC,KAAZ,EAA6B;AAAA;;AAC3B,wCAAMA,KAAN;AAD2B,YA7B7BC,cA6B6B,GA7BPJ,iBAAiB,CAAC,MAAKG,KAAN,gCA6BV;AAAA,YA5B7BE,0BA4B6B,GA5BkB,IA4BlB;AAAA,YA3B7BC,SA2B6B;AAAA,YAnB7BC,KAmB6B,GAnBd;AACbC,QAAAA,QAAQ,+BADK;AAEbC,QAAAA,WAAW,EAAE,KAFA;AAGbC,QAAAA,yBAAyB,EAAE,SAHd;AAIbrC,QAAAA,UAAU,EACR,OAAO,MAAK8B,KAAL,CAAWQ,iBAAlB,KAAwC,QAAxC,GACI,MAAKR,KAAL,CAAWQ,iBADf,GAEI,CAPO;AAQbC,QAAAA,SAAS,EACP,OAAO,MAAKT,KAAL,CAAWU,gBAAlB,KAAuC,QAAvC,GACI,MAAKV,KAAL,CAAWU,gBADf,GAEI,CAXO;AAYbC,QAAAA,wBAAwB,EAAE,KAZb;AAabC,QAAAA,uBAAuB,EAAE;AAbZ,OAmBc;AAAA,YAwQ7BC,oBAxQ6B;AAAA,YAkR7BA,oBAlR6B,GAkRNC,UAAU,CAC/B,UACEC,wBADF,EAEEC,uBAFF,EAGEC,qBAHF,EAIEC,oBAJF,EAKEC,uBALF,EAMEC,sBANF,EAOEC,oBAPF,EAQEC,mBARF;AAAA,eAUI,MAAKtB,KAAL,CAAWuB,eAAb,CAA6D;AAC3DR,UAAAA,wBAAwB,EAAxBA,wBAD2D;AAE3DC,UAAAA,uBAAuB,EAAvBA,uBAF2D;AAG3DC,UAAAA,qBAAqB,EAArBA,qBAH2D;AAI3DC,UAAAA,oBAAoB,EAApBA,oBAJ2D;AAK3DC,UAAAA,uBAAuB,EAAvBA,uBAL2D;AAM3DC,UAAAA,sBAAsB,EAAtBA,sBAN2D;AAO3DC,UAAAA,oBAAoB,EAApBA,oBAP2D;AAQ3DC,UAAAA,mBAAmB,EAAnBA;AAR2D,SAA7D,CAVF;AAAA,OAD+B,CAlRJ;AAAA,YAyS7BE,aAzS6B;AAAA,YAgT7BA,aAhT6B,GAgTbV,UAAU,CACxB,UACE5C,UADF,EAEEuC,SAFF,EAGEF,yBAHF,EAIEK,uBAJF,EAKED,wBALF;AAAA,eAOI,MAAKX,KAAL,CAAWyB,QAAb,CAA+C;AAC7ClB,UAAAA,yBAAyB,EAAzBA,yBAD6C;AAE7CrC,UAAAA,UAAU,EAAVA,UAF6C;AAG7CuC,UAAAA,SAAS,EAATA,SAH6C;AAI7CG,UAAAA,uBAAuB,EAAvBA,uBAJ6C;AAK7CD,UAAAA,wBAAwB,EAAxBA;AAL6C,SAA/C,CAPF;AAAA,OADwB,CAhTG;AAAA,YAqX7Be,aArX6B;;AAAA,YAsX7BA,aAtX6B,GAsXb,UAACnD,QAAD,EAAmBF,WAAnB,EAAmD;AACjE,0BAA8C,MAAK2B,KAAnD;AAAA,YAAQ2B,WAAR,eAAQA,WAAR;AAAA,YAAqB5D,SAArB,eAAqBA,SAArB;AAAA,YAAgC6D,SAAhC,eAAgCA,SAAhC;;AAEA,YAAMC,cAAc,GAAG,MAAKC,kBAAL,CACrBhC,qCAAqC,IAAI6B,WADpB,EAErB7B,qCAAqC,IAAI/B,SAFpB,EAGrB+B,qCAAqC,IAAI8B,SAHpB,CAAvB;;AAMA,YAAMG,GAAG,GAAMxD,QAAN,SAAkBF,WAA3B;AAEA,YAAInB,KAAJ;;AACA,YAAI2E,cAAc,CAACG,cAAf,CAA8BD,GAA9B,CAAJ,EAAwC;AACtC7E,UAAAA,KAAK,GAAG2E,cAAc,CAACE,GAAD,CAAtB;AACD,SAFD,MAEO;AACL,cAAME,OAAM,GAAGhD,eAAe,CAC5B,MAAKe,KADuB,EAE5B3B,WAF4B,EAG5B,MAAK4B,cAHuB,CAA9B;;AAKA,cAAMiC,KAAK,GAAGnE,SAAS,KAAK,KAA5B;AACA8D,UAAAA,cAAc,CAACE,GAAD,CAAd,GAAsB7E,KAAK,GAAG;AAC5BiF,YAAAA,QAAQ,EAAE,UADkB;AAE5BC,YAAAA,IAAI,EAAEF,KAAK,GAAGG,SAAH,GAAeJ,OAFE;AAG5BK,YAAAA,KAAK,EAAEJ,KAAK,GAAGD,OAAH,GAAYI,SAHI;AAI5BE,YAAAA,GAAG,EAAE7C,YAAY,CAAC,MAAKM,KAAN,EAAazB,QAAb,EAAuB,MAAK0B,cAA5B,CAJW;AAK5B7C,YAAAA,MAAM,EAAEqC,YAAY,CAAC,MAAKO,KAAN,EAAazB,QAAb,EAAuB,MAAK0B,cAA5B,CALQ;AAM5B9C,YAAAA,KAAK,EAAEiC,cAAc,CAAC,MAAKY,KAAN,EAAa3B,WAAb,EAA0B,MAAK4B,cAA/B;AANO,WAA9B;AAQD;;AAED,eAAO/C,KAAP;AACD,OAtZ4B;;AAAA,YAwZ7B4E,kBAxZ6B;AAAA,YAyZ7BA,kBAzZ6B,GAyZRhB,UAAU,CAAC,UAAC0B,CAAD,EAASC,EAAT,EAAkBC,GAAlB;AAAA,eAAgC,EAAhC;AAAA,OAAD,CAzZF;;AAAA,YA2f7BC,SA3f6B,GA2fjB,UAACC,KAAD,EAA8B;AACxC,mCAOIA,KAAK,CAACC,aAPV;AAAA,YACEC,YADF,wBACEA,YADF;AAAA,YAEErF,WAFF,wBAEEA,WAFF;AAAA,YAGES,UAHF,wBAGEA,UAHF;AAAA,YAIEuC,SAJF,wBAIEA,SAJF;AAAA,YAKEsC,YALF,wBAKEA,YALF;AAAA,YAMEC,WANF,wBAMEA,WANF;;AAQA,cAAKC,QAAL,CAAc,UAAAC,SAAS,EAAI;AACzB,cACEA,SAAS,CAAChF,UAAV,KAAyBA,UAAzB,IACAgF,SAAS,CAACzC,SAAV,KAAwBA,SAF1B,EAGE;AACA;AACA;AACA;AACA,mBAAO,IAAP;AACD;;AAED,cAAQ1C,SAAR,GAAsB,MAAKiC,KAA3B,CAAQjC,SAAR,CAXyB;AAczB;AACA;AACA;;AACA,cAAIoF,oBAAoB,GAAGjF,UAA3B;;AACA,cAAIH,SAAS,KAAK,KAAlB,EAAyB;AACvB,oBAAQH,gBAAgB,EAAxB;AACE,mBAAK,UAAL;AACEuF,gBAAAA,oBAAoB,GAAG,CAACjF,UAAxB;AACA;;AACF,mBAAK,qBAAL;AACEiF,gBAAAA,oBAAoB,GAAGH,WAAW,GAAGvF,WAAd,GAA4BS,UAAnD;AACA;AANJ;AAQD,WA3BwB;;;AA8BzBiF,UAAAA,oBAAoB,GAAGC,IAAI,CAACC,GAAL,CACrB,CADqB,EAErBD,IAAI,CAACE,GAAL,CAASH,oBAAT,EAA+BH,WAAW,GAAGvF,WAA7C,CAFqB,CAAvB;AAIA,cAAM8F,mBAAmB,GAAGH,IAAI,CAACC,GAAL,CAC1B,CAD0B,EAE1BD,IAAI,CAACE,GAAL,CAAS7C,SAAT,EAAoBsC,YAAY,GAAGD,YAAnC,CAF0B,CAA5B;AAKA,iBAAO;AACLxC,YAAAA,WAAW,EAAE,IADR;AAELC,YAAAA,yBAAyB,EACvB2C,SAAS,CAAChF,UAAV,GAAuBA,UAAvB,GAAoC,SAApC,GAAgD,UAH7C;AAILA,YAAAA,UAAU,EAAEiF,oBAJP;AAKL1C,YAAAA,SAAS,EAAE8C,mBALN;AAML3C,YAAAA,uBAAuB,EACrBsC,SAAS,CAACzC,SAAV,GAAsBA,SAAtB,GAAkC,SAAlC,GAA8C,UAP3C;AAQLE,YAAAA,wBAAwB,EAAE;AARrB,WAAP;AAUD,SAjDD,EAiDG,MAAK6C,0BAjDR;AAkDD,OAtjB4B;;AAAA,YAwjB7BC,eAxjB6B,GAwjBX,UAACC,GAAD,EAAoB;AACpC,YAAQC,QAAR,GAAqB,MAAK3D,KAA1B,CAAQ2D,QAAR;AAEA,cAAKxD,SAAL,GAAmBuD,GAAnB;;AAEA,YAAI,OAAOC,QAAP,KAAoB,UAAxB,EAAoC;AAClCA,UAAAA,QAAQ,CAACD,GAAD,CAAR;AACD,SAFD,MAEO,IACLC,QAAQ,IAAI,IAAZ,IACA,OAAOA,QAAP,KAAoB,QADpB,IAEAA,QAAQ,CAAC3B,cAAT,CAAwB,SAAxB,CAHK,EAIL;AACA2B,UAAAA,QAAQ,CAACC,OAAT,GAAmBF,GAAnB;AACD;AACF,OAtkB4B;;AAAA,YAwkB7BF,0BAxkB6B,GAwkBA,YAAM;AACjC,YAAI,MAAKtD,0BAAL,KAAoC,IAAxC,EAA8C;AAC5CjE,UAAAA,aAAa,CAAC,MAAKiE,0BAAN,CAAb;AACD;;AAED,cAAKA,0BAAL,GAAkC7D,cAAc,CAC9C,MAAKwH,iBADyC,EAE9C1F,8BAF8C,CAAhD;AAID,OAjlB4B;;AAAA,YAmlB7B0F,iBAnlB6B,GAmlBT,YAAM;AACxB,cAAK3D,0BAAL,GAAkC,IAAlC;;AAEA,cAAK+C,QAAL,CAAc;AAAE3C,UAAAA,WAAW,EAAE;AAAf,SAAd,EAAsC,YAAM;AAC1C;AACA;AACA,gBAAKwB,kBAAL,CAAwB,CAAC,CAAzB;AACD,SAJD;AAKD,OA3lB4B;;AAAA;AAE5B;;AAhCH,SAkCSgC,wBAlCT,GAkCE,kCACEC,SADF,EAEEb,SAFF,EAGwB;AACtBc,MAAAA,mBAAmB,CAACD,SAAD,EAAYb,SAAZ,CAAnB;AACAnD,MAAAA,aAAa,CAACgE,SAAD,CAAb;AACA,aAAO,IAAP;AACD,KAzCH;;AAAA;;AAAA,WA2CEE,QA3CF,GA2CE,yBAMS;AAAA,UALP/F,UAKO,SALPA,UAKO;AAAA,UAJPuC,SAIO,SAJPA,SAIO;;AACP,UAAIvC,UAAU,KAAKmE,SAAnB,EAA8B;AAC5BnE,QAAAA,UAAU,GAAGkF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYnF,UAAZ,CAAb;AACD;;AACD,UAAIuC,SAAS,KAAK4B,SAAlB,EAA6B;AAC3B5B,QAAAA,SAAS,GAAG2C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY5C,SAAZ,CAAZ;AACD;;AAED,WAAKwC,QAAL,CAAc,UAAAC,SAAS,EAAI;AACzB,YAAIhF,UAAU,KAAKmE,SAAnB,EAA8B;AAC5BnE,UAAAA,UAAU,GAAGgF,SAAS,CAAChF,UAAvB;AACD;;AACD,YAAIuC,SAAS,KAAK4B,SAAlB,EAA6B;AAC3B5B,UAAAA,SAAS,GAAGyC,SAAS,CAACzC,SAAtB;AACD;;AAED,YACEyC,SAAS,CAAChF,UAAV,KAAyBA,UAAzB,IACAgF,SAAS,CAACzC,SAAV,KAAwBA,SAF1B,EAGE;AACA,iBAAO,IAAP;AACD;;AAED,eAAO;AACLF,UAAAA,yBAAyB,EACvB2C,SAAS,CAAChF,UAAV,GAAuBA,UAAvB,GAAoC,SAApC,GAAgD,UAF7C;AAGLA,UAAAA,UAAU,EAAEA,UAHP;AAILuC,UAAAA,SAAS,EAAEA,SAJN;AAKLE,UAAAA,wBAAwB,EAAE,IALrB;AAMLC,UAAAA,uBAAuB,EACrBsC,SAAS,CAACzC,SAAV,GAAsBA,SAAtB,GAAkC,SAAlC,GAA8C;AAP3C,SAAP;AASD,OAxBD,EAwBG,KAAK+C,0BAxBR;AAyBD,KAlFH;;AAAA,WAoFEU,YApFF,GAoFE,6BAQS;AAAA,8BAPPC,KAOO;AAAA,UAPPA,KAOO,4BAPC,MAOD;AAAA,UANP9F,WAMO,SANPA,WAMO;AAAA,UALPE,QAKO,SALPA,QAKO;AACP,yBAAiD,KAAKyB,KAAtD;AAAA,UAAQoE,WAAR,gBAAQA,WAAR;AAAA,UAAqBhH,MAArB,gBAAqBA,MAArB;AAAA,UAA6BiH,QAA7B,gBAA6BA,QAA7B;AAAA,UAAuClH,KAAvC,gBAAuCA,KAAvC;AACA,wBAAkC,KAAKiD,KAAvC;AAAA,UAAQlC,UAAR,eAAQA,UAAR;AAAA,UAAoBuC,SAApB,eAAoBA,SAApB;AACA,UAAM6D,aAAa,GAAGzH,gBAAgB,EAAtC;;AAEA,UAAIwB,WAAW,KAAKgE,SAApB,EAA+B;AAC7BhE,QAAAA,WAAW,GAAG+E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASjF,WAAT,EAAsB+F,WAAW,GAAG,CAApC,CAAZ,CAAd;AACD;;AACD,UAAI7F,QAAQ,KAAK8D,SAAjB,EAA4B;AAC1B9D,QAAAA,QAAQ,GAAG6E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS/E,QAAT,EAAmB8F,QAAQ,GAAG,CAA9B,CAAZ,CAAX;AACD;;AAED,UAAME,oBAAoB,GAAGlF,uBAAuB,CAClD,KAAKW,KAD6C,EAElD,KAAKC,cAF6C,CAApD;AAIA,UAAMuE,mBAAmB,GAAGlF,sBAAsB,CAChD,KAAKU,KAD2C,EAEhD,KAAKC,cAF2C,CAAlD,CAhBO;AAsBP;AACA;;AACA,UAAMwE,uBAAuB,GAC3BD,mBAAmB,GAAGrH,KAAtB,GAA8BmH,aAA9B,GAA8C,CADhD;AAEA,UAAMI,qBAAqB,GACzBH,oBAAoB,GAAGnH,MAAvB,GAAgCkH,aAAhC,GAAgD,CADlD;AAGA,WAAKL,QAAL,CAAc;AACZ/F,QAAAA,UAAU,EACRG,WAAW,KAAKgE,SAAhB,GACI9C,8BAA8B,CAC5B,KAAKS,KADuB,EAE5B3B,WAF4B,EAG5B8F,KAH4B,EAI5BjG,UAJ4B,EAK5B,KAAK+B,cALuB,EAM5ByE,qBAN4B,CADlC,GASIxG,UAXM;AAYZuC,QAAAA,SAAS,EACPlC,QAAQ,KAAK8D,SAAb,GACI7C,2BAA2B,CACzB,KAAKQ,KADoB,EAEzBzB,QAFyB,EAGzB4F,KAHyB,EAIzB1D,SAJyB,EAKzB,KAAKR,cALoB,EAMzBwE,uBANyB,CAD/B,GASIhE;AAtBM,OAAd;AAwBD,KAjJH;;AAAA,WAmJEkE,iBAnJF,GAmJE,6BAAoB;AAClB,yBAAgD,KAAK3E,KAArD;AAAA,UAAQQ,iBAAR,gBAAQA,iBAAR;AAAA,UAA2BE,gBAA3B,gBAA2BA,gBAA3B;;AAEA,UAAI,KAAKP,SAAL,IAAkB,IAAtB,EAA4B;AAC1B,YAAMwD,QAAQ,GAAK,KAAKxD,SAAxB;;AACA,YAAI,OAAOK,iBAAP,KAA6B,QAAjC,EAA2C;AACzCmD,UAAAA,QAAQ,CAACzF,UAAT,GAAsBsC,iBAAtB;AACD;;AACD,YAAI,OAAOE,gBAAP,KAA4B,QAAhC,EAA0C;AACxCiD,UAAAA,QAAQ,CAAClD,SAAT,GAAqBC,gBAArB;AACD;AACF;;AAED,WAAKkE,mBAAL;AACD,KAjKH;;AAAA,WAmKEC,kBAnKF,GAmKE,8BAAqB;AACnB,UAAQ9G,SAAR,GAAsB,KAAKiC,KAA3B,CAAQjC,SAAR;AACA,yBAA4D,KAAKqC,KAAjE;AAAA,UAAQlC,UAAR,gBAAQA,UAAR;AAAA,UAAoBuC,SAApB,gBAAoBA,SAApB;AAAA,UAA+BE,wBAA/B,gBAA+BA,wBAA/B;;AAEA,UAAIA,wBAAwB,IAAI,KAAKR,SAAL,IAAkB,IAAlD,EAAwD;AACtD;AACA;AACA;AACA,YAAMwD,QAAQ,GAAK,KAAKxD,SAAxB;;AACA,YAAIpC,SAAS,KAAK,KAAlB,EAAyB;AACvB,kBAAQH,gBAAgB,EAAxB;AACE,iBAAK,UAAL;AACE+F,cAAAA,QAAQ,CAACzF,UAAT,GAAsB,CAACA,UAAvB;AACA;;AACF,iBAAK,oBAAL;AACEyF,cAAAA,QAAQ,CAACzF,UAAT,GAAsBA,UAAtB;AACA;;AACF;AACE,kBAAQT,WAAR,GAAqCkG,QAArC,CAAQlG,WAAR;AAAA,kBAAqBuF,WAArB,GAAqCW,QAArC,CAAqBX,WAArB;AACAW,cAAAA,QAAQ,CAACzF,UAAT,GAAsB8E,WAAW,GAAGvF,WAAd,GAA4BS,UAAlD;AACA;AAVJ;AAYD,SAbD,MAaO;AACLyF,UAAAA,QAAQ,CAACzF,UAAT,GAAsBkF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYnF,UAAZ,CAAtB;AACD;;AAEDyF,QAAAA,QAAQ,CAAClD,SAAT,GAAqB2C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY5C,SAAZ,CAArB;AACD;;AAED,WAAKmE,mBAAL;AACD,KAjMH;;AAAA,WAmMEE,oBAnMF,GAmME,gCAAuB;AACrB,UAAI,KAAK5E,0BAAL,KAAoC,IAAxC,EAA8C;AAC5CjE,QAAAA,aAAa,CAAC,KAAKiE,0BAAN,CAAb;AACD;AACF,KAvMH;;AAAA,WAyME6E,MAzMF,GAyME,kBAAS;AACP,yBAiBI,KAAK/E,KAjBT;AAAA,UACEgF,QADF,gBACEA,QADF;AAAA,UAEEC,SAFF,gBAEEA,SAFF;AAAA,UAGEb,WAHF,gBAGEA,WAHF;AAAA,UAIErG,SAJF,gBAIEA,SAJF;AAAA,UAKEX,MALF,gBAKEA,MALF;AAAA,UAME8H,QANF,gBAMEA,QANF;AAAA,UAOEC,gBAPF,gBAOEA,gBAPF;AAAA,UAQEC,YARF,gBAQEA,YARF;AAAA,UASEC,QATF,gBASEA,QATF;AAAA,8CAUEC,OAVF;AAAA,UAUEA,OAVF,qCAUYlH,cAVZ;AAAA,UAWEmH,gBAXF,gBAWEA,gBAXF;AAAA,UAYEC,YAZF,gBAYEA,YAZF;AAAA,UAaEnB,QAbF,gBAaEA,QAbF;AAAA,UAcEnH,KAdF,gBAcEA,KAdF;AAAA,UAeEuI,cAfF,gBAeEA,cAfF;AAAA,UAgBEtI,KAhBF,gBAgBEA,KAhBF;AAkBA,UAAQmD,WAAR,GAAwB,KAAKF,KAA7B,CAAQE,WAAR;;AAEA,kCAGI,KAAKoF,2BAAL,EAHJ;AAAA,UACEC,gBADF;AAAA,UAEEC,eAFF;;AAIA,kCAAsC,KAAKC,yBAAL,EAAtC;AAAA,UAAOC,aAAP;AAAA,UAAsBC,YAAtB;;AAEA,UAAMC,KAAK,GAAG,EAAd;;AACA,UAAI5B,WAAW,GAAG,CAAd,IAAmBC,QAAvB,EAAiC;AAC/B,aACE,IAAI9F,SAAQ,GAAGuH,aADjB,EAEEvH,SAAQ,IAAIwH,YAFd,EAGExH,SAAQ,EAHV,EAIE;AACA,eACE,IAAIF,YAAW,GAAGsH,gBADpB,EAEEtH,YAAW,IAAIuH,eAFjB,EAGEvH,YAAW,EAHb,EAIE;AACA2H,YAAAA,KAAK,CAACC,IAAN,CACEhJ,aAAa,CAAC+H,QAAD,EAAW;AACtB3G,cAAAA,WAAW,EAAXA,YADsB;AAEtBC,cAAAA,IAAI,EAAE+G,QAFgB;AAGtB/E,cAAAA,WAAW,EAAEmF,cAAc,GAAGnF,WAAH,GAAiB+B,SAHtB;AAItBN,cAAAA,GAAG,EAAEuD,OAAO,CAAC;AAAEjH,gBAAAA,WAAW,EAAXA,YAAF;AAAeC,gBAAAA,IAAI,EAAE+G,QAArB;AAA+B9G,gBAAAA,QAAQ,EAARA;AAA/B,eAAD,CAJU;AAKtBA,cAAAA,QAAQ,EAARA,SALsB;AAMtBrB,cAAAA,KAAK,EAAE,KAAKwE,aAAL,CAAmBnD,SAAnB,EAA6BF,YAA7B;AANe,aAAX,CADf;AAUD;AACF;AACF,OAnDM;AAsDP;;;AACA,UAAMkG,oBAAoB,GAAGlF,uBAAuB,CAClD,KAAKW,KAD6C,EAElD,KAAKC,cAF6C,CAApD;AAIA,UAAMuE,mBAAmB,GAAGlF,sBAAsB,CAChD,KAAKU,KAD2C,EAEhD,KAAKC,cAF2C,CAAlD;AAKA,aAAOhD,aAAa,CAClBsI,gBAAgB,IAAIC,YAApB,IAAoC,KADlB,EAElB;AACEP,QAAAA,SAAS,EAATA,SADF;AAEExD,QAAAA,QAAQ,EAAE,KAAKkB,SAFjB;AAGEe,QAAAA,GAAG,EAAE,KAAKD,eAHZ;AAIEvG,QAAAA,KAAK;AACHiF,UAAAA,QAAQ,EAAE,UADP;AAEH/E,UAAAA,MAAM,EAANA,MAFG;AAGHD,UAAAA,KAAK,EAALA,KAHG;AAIHE,UAAAA,QAAQ,EAAE,MAJP;AAKH6I,UAAAA,uBAAuB,EAAE,OALtB;AAMHC,UAAAA,UAAU,EAAE,WANT;AAOHpI,UAAAA,SAAS,EAATA;AAPG,WAQAb,KARA;AAJP,OAFkB,EAiBlBD,aAAa,CAACkI,gBAAgB,IAAIC,YAApB,IAAoC,KAArC,EAA4C;AACvDJ,QAAAA,QAAQ,EAAEgB,KAD6C;AAEvDtC,QAAAA,GAAG,EAAEwB,QAFkD;AAGvDhI,QAAAA,KAAK,EAAE;AACLE,UAAAA,MAAM,EAAEmH,oBADH;AAEL6B,UAAAA,aAAa,EAAE9F,WAAW,GAAG,MAAH,GAAY+B,SAFjC;AAGLlF,UAAAA,KAAK,EAAEqH;AAHF;AAHgD,OAA5C,CAjBK,CAApB;AA2BD,KApSH;;AAAA,WA+VEI,mBA/VF,GA+VE,+BAAsB;AACpB,yBAA6D,KAAK5E,KAAlE;AAAA,UAAQoE,WAAR,gBAAQA,WAAR;AAAA,UAAqB7C,eAArB,gBAAqBA,eAArB;AAAA,UAAsCE,QAAtC,gBAAsCA,QAAtC;AAAA,UAAgD4C,QAAhD,gBAAgDA,QAAhD;;AAEA,UAAI,OAAO9C,eAAP,KAA2B,UAA/B,EAA2C;AACzC,YAAI6C,WAAW,GAAG,CAAd,IAAmBC,QAAQ,GAAG,CAAlC,EAAqC;AACnC,uCAKI,KAAKqB,2BAAL,EALJ;AAAA,cACE3E,yBADF;AAAA,cAEEC,wBAFF;AAAA,cAGEG,wBAHF;AAAA,cAIEC,uBAJF;;AAMA,uCAKI,KAAKyE,yBAAL,EALJ;AAAA,cACE5E,sBADF;AAAA,cAEEC,qBAFF;AAAA,cAGEG,qBAHF;AAAA,cAIEC,oBAJF;;AAMA,eAAKT,oBAAL,CACEE,yBADF,EAEEC,wBAFF,EAGEC,sBAHF,EAIEC,qBAJF,EAKEC,wBALF,EAMEC,uBANF,EAOEC,qBAPF,EAQEC,oBARF;AAUD;AACF;;AAED,UAAI,OAAOG,QAAP,KAAoB,UAAxB,EAAoC;AAClC,2BAMI,KAAKrB,KANT;AAAA,YACEG,0BADF,gBACEA,yBADF;AAAA,YAEErC,WAFF,gBAEEA,UAFF;AAAA,YAGEuC,UAHF,gBAGEA,SAHF;AAAA,YAIEE,yBAJF,gBAIEA,wBAJF;AAAA,YAKEC,wBALF,gBAKEA,uBALF;;AAOA,aAAKY,aAAL,CACEtD,WADF,EAEEuC,UAFF,EAGEF,0BAHF,EAIEK,wBAJF,EAKED,yBALF;AAOD;AACF,KA7YH;AAgZE;AACA;AACA;AAlZF;;AAAA,WAybE+E,2BAzbF,GAybE,uCAAgE;AAC9D,yBAMI,KAAK1F,KANT;AAAA,UACEoE,WADF,gBACEA,WADF;AAAA,UAEEiC,mBAFF,gBAEEA,mBAFF;AAAA,UAGEC,oBAHF,gBAGEA,oBAHF;AAAA,UAIEC,aAJF,gBAIEA,aAJF;AAAA,UAKElC,QALF,gBAKEA,QALF;AAOA,yBAA+D,KAAKjE,KAApE;AAAA,UAAQG,yBAAR,gBAAQA,yBAAR;AAAA,UAAmCD,WAAnC,gBAAmCA,WAAnC;AAAA,UAAgDpC,UAAhD,gBAAgDA,UAAhD;AAEA,UAAMsI,qBAA6B,GACjCH,mBAAmB,IAAIC,oBAAvB,IAA+CC,aAA/C,IAAgE,CADlE;;AAGA,UAAInC,WAAW,KAAK,CAAhB,IAAqBC,QAAQ,KAAK,CAAtC,EAAyC;AACvC,eAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;AACD;;AAED,UAAMoC,UAAU,GAAGvH,4BAA4B,CAC7C,KAAKc,KADwC,EAE7C9B,UAF6C,EAG7C,KAAK+B,cAHwC,CAA/C;AAKA,UAAMyG,SAAS,GAAGvH,+BAA+B,CAC/C,KAAKa,KAD0C,EAE/CyG,UAF+C,EAG/CvI,UAH+C,EAI/C,KAAK+B,cAJ0C,CAAjD,CAtB8D;AA8B9D;;AACA,UAAM0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBC,yBAAyB,KAAK,UAA9C,GACI6C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;AAIA,UAAMI,eAAe,GACnB,CAACtG,WAAD,IAAgBC,yBAAyB,KAAK,SAA9C,GACI6C,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;AAKA,aAAO,CACLpD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASc,WAAW,GAAG,CAAvB,EAA0BsC,SAAS,GAAGE,eAAtC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;AAMD,KAveH;;AAAA,WAyeEb,yBAzeF,GAyeE,qCAA8D;AAC5D,yBAMI,KAAK7F,KANT;AAAA,UACEoE,WADF,gBACEA,WADF;AAAA,UAEEmC,aAFF,gBAEEA,aAFF;AAAA,UAGEM,gBAHF,gBAGEA,gBAHF;AAAA,UAIEC,iBAJF,gBAIEA,iBAJF;AAAA,UAKEzC,QALF,gBAKEA,QALF;AAOA,yBAA4D,KAAKjE,KAAjE;AAAA,UAAQE,WAAR,gBAAQA,WAAR;AAAA,UAAqBM,uBAArB,gBAAqBA,uBAArB;AAAA,UAA8CH,SAA9C,gBAA8CA,SAA9C;AAEA,UAAM+F,qBAA6B,GACjCK,gBAAgB,IAAIC,iBAApB,IAAyCP,aAAzC,IAA0D,CAD5D;;AAGA,UAAInC,WAAW,KAAK,CAAhB,IAAqBC,QAAQ,KAAK,CAAtC,EAAyC;AACvC,eAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;AACD;;AAED,UAAMoC,UAAU,GAAG9G,yBAAyB,CAC1C,KAAKK,KADqC,EAE1CS,SAF0C,EAG1C,KAAKR,cAHqC,CAA5C;AAKA,UAAMyG,SAAS,GAAG9G,4BAA4B,CAC5C,KAAKI,KADuC,EAE5CyG,UAF4C,EAG5ChG,SAH4C,EAI5C,KAAKR,cAJuC,CAA9C,CAtB4D;AA8B5D;;AACA,UAAM0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBM,uBAAuB,KAAK,UAA5C,GACIwC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;AAIA,UAAMI,eAAe,GACnB,CAACtG,WAAD,IAAgBM,uBAAuB,KAAK,SAA5C,GACIwC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYmD,qBAAZ,CADJ,GAEI,CAHN;AAKA,aAAO,CACLpD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAASe,QAAQ,GAAG,CAApB,EAAuBqC,SAAS,GAAGE,eAAnC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;AAMD,KAvhBH;;AAAA;AAAA,IAA6BK,aAA7B,UAKSC,YALT,GAKwB;AACpBjJ,IAAAA,SAAS,EAAE,KADS;AAEpBsH,IAAAA,QAAQ,EAAEhD,SAFU;AAGpBoD,IAAAA,cAAc,EAAE;AAHI,GALxB;AA2nBD;;AAED,IAAMzB,mBAAmB,GAAG,SAAtBA,mBAAsB,eAajB;AAAA,MAXPgB,QAWO,SAXPA,QAWO;AAAA,MAVPjH,SAUO,SAVPA,SAUO;AAAA,MATPX,MASO,SATPA,MASO;AAAA,MARPgI,YAQO,SARPA,YAQO;AAAA,MAPPI,YAOO,SAPPA,YAOO;AAAA,MANPc,oBAMO,SANPA,oBAMO;AAAA,MALPC,aAKO,SALPA,aAKO;AAAA,MAJPO,iBAIO,SAJPA,iBAIO;AAAA,MAHP3J,KAGO,SAHPA,KAGO;AAAA,MADPkD,QACO,SADPA,QACO;;AACT,MAAI1B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,QAAI,OAAO0H,aAAP,KAAyB,QAA7B,EAAuC;AACrC,UAAI/H,wBAAwB,IAAI,CAACA,wBAAwB,CAACyI,GAAzB,CAA6B5G,QAA7B,CAAjC,EAAyE;AACvE7B,QAAAA,wBAAwB,CAAC0I,GAAzB,CAA6B7G,QAA7B;AACA8G,QAAAA,OAAO,CAACC,IAAR,CACE,iDACE,wEAFJ;AAID;AACF;;AAED,QACE,OAAOd,oBAAP,KAAgC,QAAhC,IACA,OAAOQ,iBAAP,KAA6B,QAF/B,EAGE;AACA,UACErI,mCAAmC,IACnC,CAACA,mCAAmC,CAACwI,GAApC,CAAwC5G,QAAxC,CAFH,EAGE;AACA5B,QAAAA,mCAAmC,CAACyI,GAApC,CAAwC7G,QAAxC;AACA8G,QAAAA,OAAO,CAACC,IAAR,CACE,gFACE,wEAFJ;AAID;AACF;;AAED,QAAIhC,YAAY,IAAI,IAAhB,IAAwBI,YAAY,IAAI,IAA5C,EAAkD;AAChD,UAAI9G,kBAAkB,IAAI,CAACA,kBAAkB,CAACuI,GAAnB,CAAuB5G,QAAvB,CAA3B,EAA6D;AAC3D3B,QAAAA,kBAAkB,CAACwI,GAAnB,CAAuB7G,QAAvB;AACA8G,QAAAA,OAAO,CAACC,IAAR,CACE,mEACE,qEAFJ;AAID;AACF;;AAED,QAAIpC,QAAQ,IAAI,IAAhB,EAAsB;AACpB,YAAMqC,KAAK,CACT,oDACE,qCADF,YAEMrC,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;AAKD;;AAED,YAAQjH,SAAR;AACE,WAAK,KAAL;AACA,WAAK,KAAL;AACE;AACA;;AACF;AACE,cAAMsJ,KAAK,CACT,qDACE,yCADF,WAEMtJ,SAFN,uBADS,CAAX;AANJ;;AAaA,QAAI,OAAOZ,KAAP,KAAiB,QAArB,EAA+B;AAC7B,YAAMkK,KAAK,CACT,iDACE,yCADF,YAEMlK,KAAK,KAAK,IAAV,GAAiB,MAAjB,GAA0B,OAAOA,KAFvC,wBADS,CAAX;AAKD;;AAED,QAAI,OAAOC,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAMiK,KAAK,CACT,kDACE,0CADF,YAEMjK,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAFxC,wBADS,CAAX;AAKD;AACF;AACF,CAxFD;;ACxzBA,IAAMkK,2BAA2B,GAAG,EAApC;;AAyBA,IAAMjI,uBAAuB,GAAG,SAA1BA,uBAA0B,cAG3B;AAAA,MAFDgF,QAEC,QAFDA,QAEC;AAAA,MADDkD,cACC,SADDA,cACC;AAAA,MADeC,kBACf,SADeA,kBACf;AAAA,MADmCC,oBACnC,SADmCA,oBACnC;AACH,MAAIC,uBAAuB,GAAG,CAA9B,CADG;AAIH;;AACA,MAAID,oBAAoB,IAAIpD,QAA5B,EAAsC;AACpCoD,IAAAA,oBAAoB,GAAGpD,QAAQ,GAAG,CAAlC;AACD;;AAED,MAAIoD,oBAAoB,IAAI,CAA5B,EAA+B;AAC7B,QAAME,YAAY,GAAGJ,cAAc,CAACE,oBAAD,CAAnC;AACAC,IAAAA,uBAAuB,GAAGC,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA7D;AACD;;AAED,MAAMgL,kBAAkB,GAAGvD,QAAQ,GAAGoD,oBAAX,GAAkC,CAA7D;AACA,MAAMI,0BAA0B,GAAGD,kBAAkB,GAAGJ,kBAAxD;AAEA,SAAOE,uBAAuB,GAAGG,0BAAjC;AACD,CArBD;;AAuBA,IAAMvI,sBAAsB,GAAG,SAAzBA,sBAAyB,eAO1B;AAAA,MAND8E,WAMC,SANDA,WAMC;AAAA,MAJD0D,iBAIC,SAJDA,iBAIC;AAAA,MAHDC,oBAGC,SAHDA,oBAGC;AAAA,MAFDC,uBAEC,SAFDA,uBAEC;AACH,MAAIN,uBAAuB,GAAG,CAA9B,CADG;AAIH;;AACA,MAAIM,uBAAuB,IAAI5D,WAA/B,EAA4C;AAC1C4D,IAAAA,uBAAuB,GAAG5D,WAAW,GAAG,CAAxC;AACD;;AAED,MAAI4D,uBAAuB,IAAI,CAA/B,EAAkC;AAChC,QAAML,YAAY,GAAGG,iBAAiB,CAACE,uBAAD,CAAtC;AACAN,IAAAA,uBAAuB,GAAGC,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA7D;AACD;;AAED,MAAMgL,kBAAkB,GAAGxD,WAAW,GAAG4D,uBAAd,GAAwC,CAAnE;AACA,MAAMH,0BAA0B,GAAGD,kBAAkB,GAAGG,oBAAxD;AAEA,SAAOL,uBAAuB,GAAGG,0BAAjC;AACD,CAzBD;;AA2BA,IAAMI,eAAe,GAAG,SAAlBA,eAAkB,CACtBC,QADsB,EAEtBlI,KAFsB,EAGtBmI,KAHsB,EAItBC,aAJsB,EAKL;AACjB,MAAIC,eAAJ,EAAqBC,QAArB,EAA+BC,iBAA/B;;AACA,MAAIL,QAAQ,KAAK,QAAjB,EAA2B;AACzBG,IAAAA,eAAe,GAAGD,aAAa,CAACN,iBAAhC;AACAQ,IAAAA,QAAQ,GAAKtI,KAAK,CAAC2B,WAAnB;AACA4G,IAAAA,iBAAiB,GAAGH,aAAa,CAACJ,uBAAlC;AACD,GAJD,MAIO;AACLK,IAAAA,eAAe,GAAGD,aAAa,CAACb,cAAhC;AACAe,IAAAA,QAAQ,GAAKtI,KAAK,CAAC4B,SAAnB;AACA2G,IAAAA,iBAAiB,GAAGH,aAAa,CAACX,oBAAlC;AACD;;AAED,MAAIU,KAAK,GAAGI,iBAAZ,EAA+B;AAC7B,QAAItG,MAAM,GAAG,CAAb;;AACA,QAAIsG,iBAAiB,IAAI,CAAzB,EAA4B;AAC1B,UAAMZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;AACAtG,MAAAA,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA5C;AACD;;AAED,SAAK,IAAI4L,CAAC,GAAGD,iBAAiB,GAAG,CAAjC,EAAoCC,CAAC,IAAIL,KAAzC,EAAgDK,CAAC,EAAjD,EAAqD;AACnD,UAAI5L,IAAI,GAAG0L,QAAQ,CAACE,CAAD,CAAnB;AAEAH,MAAAA,eAAe,CAACG,CAAD,CAAf,GAAqB;AACnBvG,QAAAA,MAAM,EAANA,MADmB;AAEnBrF,QAAAA,IAAI,EAAJA;AAFmB,OAArB;AAKAqF,MAAAA,MAAM,IAAIrF,IAAV;AACD;;AAED,QAAIsL,QAAQ,KAAK,QAAjB,EAA2B;AACzBE,MAAAA,aAAa,CAACJ,uBAAd,GAAwCG,KAAxC;AACD,KAFD,MAEO;AACLC,MAAAA,aAAa,CAACX,oBAAd,GAAqCU,KAArC;AACD;AACF;;AAED,SAAOE,eAAe,CAACF,KAAD,CAAtB;AACD,CA3CD;;AA6CA,IAAMM,eAAe,GAAG,SAAlBA,eAAkB,CACtBP,QADsB,EAEtBlI,KAFsB,EAGtBoI,aAHsB,EAItBnG,MAJsB,EAKnB;AACH,MAAIoG,eAAJ,EAAqBE,iBAArB;;AACA,MAAIL,QAAQ,KAAK,QAAjB,EAA2B;AACzBG,IAAAA,eAAe,GAAGD,aAAa,CAACN,iBAAhC;AACAS,IAAAA,iBAAiB,GAAGH,aAAa,CAACJ,uBAAlC;AACD,GAHD,MAGO;AACLK,IAAAA,eAAe,GAAGD,aAAa,CAACb,cAAhC;AACAgB,IAAAA,iBAAiB,GAAGH,aAAa,CAACX,oBAAlC;AACD;;AAED,MAAMiB,sBAAsB,GAC1BH,iBAAiB,GAAG,CAApB,GAAwBF,eAAe,CAACE,iBAAD,CAAf,CAAmCtG,MAA3D,GAAoE,CADtE;;AAGA,MAAIyG,sBAAsB,IAAIzG,MAA9B,EAAsC;AACpC;AACA,WAAO0G,2BAA2B,CAChCT,QADgC,EAEhClI,KAFgC,EAGhCoI,aAHgC,EAIhCG,iBAJgC,EAKhC,CALgC,EAMhCtG,MANgC,CAAlC;AAQD,GAVD,MAUO;AACL;AACA;AACA;AACA,WAAO2G,gCAAgC,CACrCV,QADqC,EAErClI,KAFqC,EAGrCoI,aAHqC,EAIrChF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkF,iBAAZ,CAJqC,EAKrCtG,MALqC,CAAvC;AAOD;AACF,CAxCD;;AA0CA,IAAM0G,2BAA2B,GAAG,SAA9BA,2BAA8B,CAClCT,QADkC,EAElClI,KAFkC,EAGlCoI,aAHkC,EAIlCS,IAJkC,EAKlCC,GALkC,EAMlC7G,MANkC,EAOvB;AACX,SAAO6G,GAAG,IAAID,IAAd,EAAoB;AAClB,QAAME,MAAM,GAAGD,GAAG,GAAG1F,IAAI,CAAC4F,KAAL,CAAW,CAACH,IAAI,GAAGC,GAAR,IAAe,CAA1B,CAArB;AACA,QAAMG,aAAa,GAAGhB,eAAe,CACnCC,QADmC,EAEnClI,KAFmC,EAGnC+I,MAHmC,EAInCX,aAJmC,CAAf,CAKpBnG,MALF;;AAOA,QAAIgH,aAAa,KAAKhH,MAAtB,EAA8B;AAC5B,aAAO8G,MAAP;AACD,KAFD,MAEO,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;AACjC6G,MAAAA,GAAG,GAAGC,MAAM,GAAG,CAAf;AACD,KAFM,MAEA,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;AACjC4G,MAAAA,IAAI,GAAGE,MAAM,GAAG,CAAhB;AACD;AACF;;AAED,MAAID,GAAG,GAAG,CAAV,EAAa;AACX,WAAOA,GAAG,GAAG,CAAb;AACD,GAFD,MAEO;AACL,WAAO,CAAP;AACD;AACF,CA/BD;;AAiCA,IAAMF,gCAAgC,GAAG,SAAnCA,gCAAmC,CACvCV,QADuC,EAEvClI,KAFuC,EAGvCoI,aAHuC,EAIvCD,KAJuC,EAKvClG,MALuC,EAM5B;AACX,MAAMiH,SAAS,GAAGhB,QAAQ,KAAK,QAAb,GAAwBlI,KAAK,CAACoE,WAA9B,GAA4CpE,KAAK,CAACqE,QAApE;AACA,MAAI8E,QAAQ,GAAG,CAAf;;AAEA,SACEhB,KAAK,GAAGe,SAAR,IACAjB,eAAe,CAACC,QAAD,EAAWlI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAAf,CAAuDnG,MAAvD,GAAgEA,MAFlE,EAGE;AACAkG,IAAAA,KAAK,IAAIgB,QAAT;AACAA,IAAAA,QAAQ,IAAI,CAAZ;AACD;;AAED,SAAOR,2BAA2B,CAChCT,QADgC,EAEhClI,KAFgC,EAGhCoI,aAHgC,EAIhChF,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAJgC,EAKhC9F,IAAI,CAAC4F,KAAL,CAAWb,KAAK,GAAG,CAAnB,CALgC,EAMhClG,MANgC,CAAlC;AAQD,CA1BD;;AA4BA,IAAMmH,6BAA6B,GAAG,SAAhCA,6BAAgC,CACpClB,QADoC,EAEpClI,KAFoC,EAGpCmI,KAHoC,EAIpChE,KAJoC,EAKpCkF,YALoC,EAMpCjB,aANoC,EAOpC9D,aAPoC,EAQzB;AACX,MAAM1H,IAAI,GAAGsL,QAAQ,KAAK,QAAb,GAAwBlI,KAAK,CAAC7C,KAA9B,GAAsC6C,KAAK,CAAC5C,MAAzD;AACA,MAAMuK,YAAY,GAAGM,eAAe,CAACC,QAAD,EAAWlI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAApC,CAFW;AAKX;;AACA,MAAMkB,kBAAkB,GACtBpB,QAAQ,KAAK,QAAb,GACI5I,sBAAsB,CAACU,KAAD,EAAQoI,aAAR,CAD1B,GAEI/I,uBAAuB,CAACW,KAAD,EAAQoI,aAAR,CAH7B;AAKA,MAAMmB,SAAS,GAAGnG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBD,IAAI,CAACE,GAAL,CAASgG,kBAAkB,GAAG1M,IAA9B,EAAoC+K,YAAY,CAAC1F,MAAjD,CAFgB,CAAlB;AAIA,MAAMuH,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBsE,YAAY,CAAC1F,MAAb,GAAsBrF,IAAtB,GAA6B0H,aAA7B,GAA6CqD,YAAY,CAAC/K,IAF1C,CAAlB;;AAKA,MAAIuH,KAAK,KAAK,OAAd,EAAuB;AACrB,QAAIkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IAAoCyM,YAAY,IAAIE,SAAS,GAAG3M,IAApE,EAA0E;AACxEuH,MAAAA,KAAK,GAAG,MAAR;AACD,KAFD,MAEO;AACLA,MAAAA,KAAK,GAAG,QAAR;AACD;AACF;;AAED,UAAQA,KAAR;AACE,SAAK,OAAL;AACE,aAAOoF,SAAP;;AACF,SAAK,KAAL;AACE,aAAOC,SAAP;;AACF,SAAK,QAAL;AACE,aAAOpG,IAAI,CAACqG,KAAL,CAAWD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CAAjD,CAAP;;AACF,SAAK,MAAL;AACA;AACE,UAAIH,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;AAC1D,eAAOF,YAAP;AACD,OAFD,MAEO,IAAIG,SAAS,GAAGD,SAAhB,EAA2B;AAChC;AACA;AACA,eAAOC,SAAP;AACD,OAJM,MAIA,IAAIH,YAAY,GAAGG,SAAnB,EAA8B;AACnC,eAAOA,SAAP;AACD,OAFM,MAEA;AACL,eAAOD,SAAP;AACD;;AAnBL;AAqBD,CAzDD;;AA2DA,IAAMG,gBAAgB,gBAAG1K,mBAAmB,CAAC;AAC3CC,EAAAA,eAAe,EAAE,yBACfe,KADe,EAEfmI,KAFe,EAGfC,aAHe;AAAA,WAIJH,eAAe,CAAC,QAAD,EAAWjI,KAAX,EAAkBmI,KAAlB,EAAyBC,aAAzB,CAAf,CAAuDnG,MAJnD;AAAA,GAD0B;AAO3C/C,EAAAA,4BAA4B,EAAE,sCAC5Bc,KAD4B,EAE5B9B,UAF4B,EAG5BkK,aAH4B;AAAA,WAIjBK,eAAe,CAAC,QAAD,EAAWzI,KAAX,EAAkBoI,aAAlB,EAAiClK,UAAjC,CAJE;AAAA,GAPa;AAa3CiB,EAAAA,+BAA+B,EAAE,yCAC/Ba,KAD+B,EAE/ByG,UAF+B,EAG/BvI,UAH+B,EAI/BkK,aAJ+B,EAKpB;AACX,QAAQhE,WAAR,GAA+BpE,KAA/B,CAAQoE,WAAR;AAAA,QAAqBjH,KAArB,GAA+B6C,KAA/B,CAAqB7C,KAArB;AAEA,QAAMwK,YAAY,GAAGM,eAAe,CAClC,QADkC,EAElCjI,KAFkC,EAGlCyG,UAHkC,EAIlC2B,aAJkC,CAApC;AAMA,QAAMmB,SAAS,GAAGrL,UAAU,GAAGf,KAA/B;AAEA,QAAI8E,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;AACA,QAAI8J,SAAS,GAAGD,UAAhB;;AAEA,WAAOC,SAAS,GAAGtC,WAAW,GAAG,CAA1B,IAA+BnC,MAAM,GAAGsH,SAA/C,EAA0D;AACxD7C,MAAAA,SAAS;AACTzE,MAAAA,MAAM,IAAIgG,eAAe,CAAC,QAAD,EAAWjI,KAAX,EAAkB0G,SAAlB,EAA6B0B,aAA7B,CAAf,CAA2DxL,IAArE;AACD;;AAED,WAAO8J,SAAP;AACD,GAtC0C;AAwC3CtH,EAAAA,cAAc,EAAE,wBACdY,KADc,EAEdmI,KAFc,EAGdC,aAHc;AAAA,WAIHA,aAAa,CAACN,iBAAd,CAAgCK,KAAhC,EAAuCvL,IAJpC;AAAA,GAxC2B;AA8C3CyC,EAAAA,uBAAuB,EAAvBA,uBA9C2C;AA+C3CC,EAAAA,sBAAsB,EAAtBA,sBA/C2C;AAiD3CC,EAAAA,8BAA8B,EAAE,wCAC9BS,KAD8B,EAE9BmI,KAF8B,EAG9BhE,KAH8B,EAI9BkF,YAJ8B,EAK9BjB,aAL8B,EAM9B9D,aAN8B;AAAA,WAQ9B8E,6BAA6B,CAC3B,QAD2B,EAE3BpJ,KAF2B,EAG3BmI,KAH2B,EAI3BhE,KAJ2B,EAK3BkF,YAL2B,EAM3BjB,aAN2B,EAO3B9D,aAP2B,CARC;AAAA,GAjDW;AAmE3C9E,EAAAA,2BAA2B,EAAE,qCAC3BQ,KAD2B,EAE3BmI,KAF2B,EAG3BhE,KAH2B,EAI3BkF,YAJ2B,EAK3BjB,aAL2B,EAM3B9D,aAN2B;AAAA,WAQ3B8E,6BAA6B,CAC3B,KAD2B,EAE3BpJ,KAF2B,EAG3BmI,KAH2B,EAI3BhE,KAJ2B,EAK3BkF,YAL2B,EAM3BjB,aAN2B,EAO3B9D,aAP2B,CARF;AAAA,GAnEc;AAqF3C5E,EAAAA,YAAY,EAAE,sBACZM,KADY,EAEZmI,KAFY,EAGZC,aAHY;AAAA,WAIDH,eAAe,CAAC,KAAD,EAAQjI,KAAR,EAAemI,KAAf,EAAsBC,aAAtB,CAAf,CAAoDnG,MAJnD;AAAA,GArF6B;AA2F3CxC,EAAAA,YAAY,EAAE,sBACZO,KADY,EAEZmI,KAFY,EAGZC,aAHY;AAAA,WAIDA,aAAa,CAACb,cAAd,CAA6BY,KAA7B,EAAoCvL,IAJnC;AAAA,GA3F6B;AAiG3C+C,EAAAA,yBAAyB,EAAE,mCACzBK,KADyB,EAEzBS,SAFyB,EAGzB2H,aAHyB;AAAA,WAIdK,eAAe,CAAC,KAAD,EAAQzI,KAAR,EAAeoI,aAAf,EAA8B3H,SAA9B,CAJD;AAAA,GAjGgB;AAuG3Cb,EAAAA,4BAA4B,EAAE,sCAC5BI,KAD4B,EAE5ByG,UAF4B,EAG5BhG,SAH4B,EAI5B2H,aAJ4B,EAKjB;AACX,QAAQ/D,QAAR,GAA6BrE,KAA7B,CAAQqE,QAAR;AAAA,QAAkBjH,MAAlB,GAA6B4C,KAA7B,CAAkB5C,MAAlB;AAEA,QAAMuK,YAAY,GAAGM,eAAe,CAClC,KADkC,EAElCjI,KAFkC,EAGlCyG,UAHkC,EAIlC2B,aAJkC,CAApC;AAMA,QAAMmB,SAAS,GAAG9I,SAAS,GAAGrD,MAA9B;AAEA,QAAI6E,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;AACA,QAAI8J,SAAS,GAAGD,UAAhB;;AAEA,WAAOC,SAAS,GAAGrC,QAAQ,GAAG,CAAvB,IAA4BpC,MAAM,GAAGsH,SAA5C,EAAuD;AACrD7C,MAAAA,SAAS;AACTzE,MAAAA,MAAM,IAAIgG,eAAe,CAAC,KAAD,EAAQjI,KAAR,EAAe0G,SAAf,EAA0B0B,aAA1B,CAAf,CAAwDxL,IAAlE;AACD;;AAED,WAAO8J,SAAP;AACD,GAhI0C;AAkI3C7G,EAAAA,iBAlI2C,6BAkIzBG,KAlIyB,EAkINK,QAlIM,EAkIwB;AACjE,gBAGML,KAHN;AAAA,QACE+H,oBADF,SACEA,oBADF;AAAA,QAEEP,kBAFF,SAEEA,kBAFF;AAKA,QAAMY,aAAa,GAAG;AACpBN,MAAAA,iBAAiB,EAAE,EADC;AAEpBC,MAAAA,oBAAoB,EAAEA,oBAAoB,IAAIT,2BAF1B;AAGpBE,MAAAA,kBAAkB,EAAEA,kBAAkB,IAAIF,2BAHtB;AAIpBU,MAAAA,uBAAuB,EAAE,CAAC,CAJN;AAKpBP,MAAAA,oBAAoB,EAAE,CAAC,CALH;AAMpBF,MAAAA,cAAc,EAAE;AANI,KAAtB;;AASAlH,IAAAA,QAAQ,CAACsJ,qBAAT,GAAiC,UAC/BtL,WAD+B,EAE/BuL,iBAF+B,EAG5B;AAAA,UADHA,iBACG;AADHA,QAAAA,iBACG,GAD2B,IAC3B;AAAA;;AACHvJ,MAAAA,QAAQ,CAACwJ,iBAAT,CAA2B;AAAExL,QAAAA,WAAW,EAAXA,WAAF;AAAeuL,QAAAA,iBAAiB,EAAjBA;AAAf,OAA3B;AACD,KALD;;AAOAvJ,IAAAA,QAAQ,CAACyJ,kBAAT,GAA8B,UAC5BvL,QAD4B,EAE5BqL,iBAF4B,EAGzB;AAAA,UADHA,iBACG;AADHA,QAAAA,iBACG,GAD2B,IAC3B;AAAA;;AACHvJ,MAAAA,QAAQ,CAACwJ,iBAAT,CAA2B;AAAEtL,QAAAA,QAAQ,EAARA,QAAF;AAAYqL,QAAAA,iBAAiB,EAAjBA;AAAZ,OAA3B;AACD,KALD;;AAOAvJ,IAAAA,QAAQ,CAACwJ,iBAAT,GAA6B,iBAQvB;AAAA,UAPJxL,WAOI,SAPJA,WAOI;AAAA,UANJE,QAMI,SANJA,QAMI;AAAA,wCALJqL,iBAKI;AAAA,UALJA,iBAKI,sCALgB,IAKhB;;AACJ,UAAI,OAAOvL,WAAP,KAAuB,QAA3B,EAAqC;AACnC+J,QAAAA,aAAa,CAACJ,uBAAd,GAAwC5E,IAAI,CAACE,GAAL,CACtC8E,aAAa,CAACJ,uBADwB,EAEtC3J,WAAW,GAAG,CAFwB,CAAxC;AAID;;AACD,UAAI,OAAOE,QAAP,KAAoB,QAAxB,EAAkC;AAChC6J,QAAAA,aAAa,CAACX,oBAAd,GAAqCrE,IAAI,CAACE,GAAL,CACnC8E,aAAa,CAACX,oBADqB,EAEnClJ,QAAQ,GAAG,CAFwB,CAArC;AAID,OAZG;AAeJ;AACA;AACA;;;AACA8B,MAAAA,QAAQ,CAACyB,kBAAT,CAA4B,CAAC,CAA7B;;AAEA,UAAI8H,iBAAJ,EAAuB;AACrBvJ,QAAAA,QAAQ,CAAC0J,WAAT;AACD;AACF,KA/BD;;AAiCA,WAAO3B,aAAP;AACD,GAjM0C;AAmM3CtI,EAAAA,qCAAqC,EAAE,KAnMI;AAqM3CC,EAAAA,aAAa,EAAE,8BAAkD;AAAA,QAA/C4B,WAA+C,SAA/CA,WAA+C;AAAA,QAAlCC,SAAkC,SAAlCA,SAAkC;;AAC/D,QAAIjD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAI,OAAO8C,WAAP,KAAuB,UAA3B,EAAuC;AACrC,cAAM0F,KAAK,CACT,uDACE,8BADF,YAGI1F,WAAW,KAAK,IAAhB,GAAuB,MAAvB,GAAgC,OAAOA,WAH3C,wBADS,CAAX;AAOD,OARD,MAQO,IAAI,OAAOC,SAAP,KAAqB,UAAzB,EAAqC;AAC1C,cAAMyF,KAAK,CACT,qDACE,8BADF,YAEMzF,SAAS,KAAK,IAAd,GAAqB,MAArB,GAA8B,OAAOA,SAF3C,wBADS,CAAX;AAKD;AACF;AACF;AAvN0C,CAAD,CAA5C;;ACtKA,IAAMzD,gCAA8B,GAAG,GAAvC;;AAEA,IAAMC,gBAAc,GAAG,SAAjBA,cAAiB,CAAC+J,KAAD,EAAgB7J,IAAhB;AAAA,SAA8B6J,KAA9B;AAAA,CAAvB;AAGA;;;AACA,IAAI6B,oBAAoB,GAAG,IAA3B;AACA,IAAItL,oBAAkB,GAAG,IAAzB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,MAAI,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOA,MAAM,CAACC,OAAd,KAA0B,WAA/D,EAA4E;AAC1EiL,IAAAA,oBAAoB,gBAAG,IAAIjL,OAAJ,EAAvB;AACAL,IAAAA,oBAAkB,gBAAG,IAAIK,OAAJ,EAArB;AACD;AACF;;AAED,AAAe,SAASkL,mBAAT,OAoBX;AAAA;;AAAA,MAnBFC,aAmBE,QAnBFA,aAmBE;AAAA,MAlBFC,qBAkBE,QAlBFA,qBAkBE;AAAA,MAjBFC,WAiBE,QAjBFA,WAiBE;AAAA,MAhBFhB,6BAgBE,QAhBFA,6BAgBE;AAAA,MAfFiB,sBAeE,QAfFA,sBAeE;AAAA,MAdFC,yBAcE,QAdFA,yBAcE;AAAA,MAbFzK,iBAaE,QAbFA,iBAaE;AAAA,MAZFC,qCAYE,QAZFA,qCAYE;AAAA,MAXFC,aAWE,QAXFA,aAWE;AACF;AAAA;;AAwBE;AACA;AACA;AACA,kBAAYC,KAAZ,EAA6B;AAAA;;AAC3B,wCAAMA,KAAN;AAD2B,YA1B7BC,cA0B6B,GA1BPJ,iBAAiB,CAAC,MAAKG,KAAN,gCA0BV;AAAA,YAzB7BG,SAyB6B;AAAA,YAxB7BD,0BAwB6B,GAxBkB,IAwBlB;AAAA,YAd7BE,KAc6B,GAdd;AACbC,QAAAA,QAAQ,+BADK;AAEbC,QAAAA,WAAW,EAAE,KAFA;AAGbiK,QAAAA,eAAe,EAAE,SAHJ;AAIblB,QAAAA,YAAY,EACV,OAAO,MAAKrJ,KAAL,CAAWwK,mBAAlB,KAA0C,QAA1C,GACI,MAAKxK,KAAL,CAAWwK,mBADf,GAEI,CAPO;AAQb7J,QAAAA,wBAAwB,EAAE;AARb,OAcc;AAAA,YA8M7BE,oBA9M6B;AAAA,YAoN7BA,oBApN6B,GAoNNC,UAAU,CAC/B,UACE2J,kBADF,EAEEC,iBAFF,EAGEC,iBAHF,EAIEC,gBAJF;AAAA,eAMI,MAAK5K,KAAL,CAAWuB,eAAb,CAA6D;AAC3DkJ,UAAAA,kBAAkB,EAAlBA,kBAD2D;AAE3DC,UAAAA,iBAAiB,EAAjBA,iBAF2D;AAG3DC,UAAAA,iBAAiB,EAAjBA,iBAH2D;AAI3DC,UAAAA,gBAAgB,EAAhBA;AAJ2D,SAA7D,CANF;AAAA,OAD+B,CApNJ;AAAA,YAmO7BpJ,aAnO6B;AAAA,YAwO7BA,aAxO6B,GAwObV,UAAU,CACxB,UACEyJ,eADF,EAEElB,YAFF,EAGE1I,wBAHF;AAAA,eAKI,MAAKX,KAAL,CAAWyB,QAAb,CAA+C;AAC7C8I,UAAAA,eAAe,EAAfA,eAD6C;AAE7ClB,UAAAA,YAAY,EAAZA,YAF6C;AAG7C1I,UAAAA,wBAAwB,EAAxBA;AAH6C,SAA/C,CALF;AAAA,OADwB,CAxOG;AAAA,YA0R7Be,aA1R6B;;AAAA,YA2R7BA,aA3R6B,GA2Rb,UAACyG,KAAD,EAA2B;AACzC,0BAAwC,MAAKnI,KAA7C;AAAA,YAAQjC,SAAR,eAAQA,SAAR;AAAA,YAAmBuK,QAAnB,eAAmBA,QAAnB;AAAA,YAA6BuC,MAA7B,eAA6BA,MAA7B;;AAEA,YAAMhJ,cAAc,GAAG,MAAKC,kBAAL,CACrBhC,qCAAqC,IAAIwI,QADpB,EAErBxI,qCAAqC,IAAI+K,MAFpB,EAGrB/K,qCAAqC,IAAI/B,SAHpB,CAAvB;;AAMA,YAAIb,KAAJ;;AACA,YAAI2E,cAAc,CAACG,cAAf,CAA8BmG,KAA9B,CAAJ,EAA0C;AACxCjL,UAAAA,KAAK,GAAG2E,cAAc,CAACsG,KAAD,CAAtB;AACD,SAFD,MAEO;AACL,cAAMlG,OAAM,GAAGiI,aAAa,CAAC,MAAKlK,KAAN,EAAamI,KAAb,EAAoB,MAAKlI,cAAzB,CAA5B;;AACA,cAAMrD,IAAI,GAAGwN,WAAW,CAAC,MAAKpK,KAAN,EAAamI,KAAb,EAAoB,MAAKlI,cAAzB,CAAxB,CAFK;;AAKL,cAAM6K,YAAY,GAChB/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAD3C;AAGA,cAAM3I,KAAK,GAAGnE,SAAS,KAAK,KAA5B;AACA,cAAMgN,gBAAgB,GAAGD,YAAY,GAAG7I,OAAH,GAAY,CAAjD;AACAJ,UAAAA,cAAc,CAACsG,KAAD,CAAd,GAAwBjL,KAAK,GAAG;AAC9BiF,YAAAA,QAAQ,EAAE,UADoB;AAE9BC,YAAAA,IAAI,EAAEF,KAAK,GAAGG,SAAH,GAAe0I,gBAFI;AAG9BzI,YAAAA,KAAK,EAAEJ,KAAK,GAAG6I,gBAAH,GAAsB1I,SAHJ;AAI9BE,YAAAA,GAAG,EAAE,CAACuI,YAAD,GAAgB7I,OAAhB,GAAyB,CAJA;AAK9B7E,YAAAA,MAAM,EAAE,CAAC0N,YAAD,GAAgBlO,IAAhB,GAAuB,MALD;AAM9BO,YAAAA,KAAK,EAAE2N,YAAY,GAAGlO,IAAH,GAAU;AANC,WAAhC;AAQD;;AAED,eAAOM,KAAP;AACD,OA5T4B;;AAAA,YA8T7B4E,kBA9T6B;AAAA,YA+T7BA,kBA/T6B,GA+TRhB,UAAU,CAAC,UAAC0B,CAAD,EAASC,EAAT,EAAkBC,GAAlB;AAAA,eAAgC,EAAhC;AAAA,OAAD,CA/TF;;AAAA,YAwW7BsI,mBAxW6B,GAwWP,UAACpI,KAAD,EAA8B;AAClD,mCAAiDA,KAAK,CAACC,aAAvD;AAAA,YAAQpF,WAAR,wBAAQA,WAAR;AAAA,YAAqBS,UAArB,wBAAqBA,UAArB;AAAA,YAAiC8E,WAAjC,wBAAiCA,WAAjC;;AACA,cAAKC,QAAL,CAAc,UAAAC,SAAS,EAAI;AACzB,cAAIA,SAAS,CAACmG,YAAV,KAA2BnL,UAA/B,EAA2C;AACzC;AACA;AACA;AACA,mBAAO,IAAP;AACD;;AAED,cAAQH,SAAR,GAAsB,MAAKiC,KAA3B,CAAQjC,SAAR;AAEA,cAAIsL,YAAY,GAAGnL,UAAnB;;AACA,cAAIH,SAAS,KAAK,KAAlB,EAAyB;AACvB;AACA;AACA;AACA;AACA,oBAAQH,gBAAgB,EAAxB;AACE,mBAAK,UAAL;AACEyL,gBAAAA,YAAY,GAAG,CAACnL,UAAhB;AACA;;AACF,mBAAK,qBAAL;AACEmL,gBAAAA,YAAY,GAAGrG,WAAW,GAAGvF,WAAd,GAA4BS,UAA3C;AACA;AANJ;AAQD,WAxBwB;;;AA2BzBmL,UAAAA,YAAY,GAAGjG,IAAI,CAACC,GAAL,CACb,CADa,EAEbD,IAAI,CAACE,GAAL,CAAS+F,YAAT,EAAuBrG,WAAW,GAAGvF,WAArC,CAFa,CAAf;AAKA,iBAAO;AACL6C,YAAAA,WAAW,EAAE,IADR;AAELiK,YAAAA,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBA,YAAzB,GAAwC,SAAxC,GAAoD,UAHjD;AAILA,YAAAA,YAAY,EAAZA,YAJK;AAKL1I,YAAAA,wBAAwB,EAAE;AALrB,WAAP;AAOD,SAvCD,EAuCG,MAAK6C,0BAvCR;AAwCD,OAlZ4B;;AAAA,YAoZ7ByH,iBApZ6B,GAoZT,UAACrI,KAAD,EAA8B;AAChD,oCAAkDA,KAAK,CAACC,aAAxD;AAAA,YAAQC,YAAR,yBAAQA,YAAR;AAAA,YAAsBC,YAAtB,yBAAsBA,YAAtB;AAAA,YAAoCtC,SAApC,yBAAoCA,SAApC;;AACA,cAAKwC,QAAL,CAAc,UAAAC,SAAS,EAAI;AACzB,cAAIA,SAAS,CAACmG,YAAV,KAA2B5I,SAA/B,EAA0C;AACxC;AACA;AACA;AACA,mBAAO,IAAP;AACD,WANwB;;;AASzB,cAAM4I,YAAY,GAAGjG,IAAI,CAACC,GAAL,CACnB,CADmB,EAEnBD,IAAI,CAACE,GAAL,CAAS7C,SAAT,EAAoBsC,YAAY,GAAGD,YAAnC,CAFmB,CAArB;AAKA,iBAAO;AACLxC,YAAAA,WAAW,EAAE,IADR;AAELiK,YAAAA,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBA,YAAzB,GAAwC,SAAxC,GAAoD,UAHjD;AAILA,YAAAA,YAAY,EAAZA,YAJK;AAKL1I,YAAAA,wBAAwB,EAAE;AALrB,WAAP;AAOD,SArBD,EAqBG,MAAK6C,0BArBR;AAsBD,OA5a4B;;AAAA,YA8a7BC,eA9a6B,GA8aX,UAACC,GAAD,EAAoB;AACpC,YAAQC,QAAR,GAAqB,MAAK3D,KAA1B,CAAQ2D,QAAR;AAEA,cAAKxD,SAAL,GAAmBuD,GAAnB;;AAEA,YAAI,OAAOC,QAAP,KAAoB,UAAxB,EAAoC;AAClCA,UAAAA,QAAQ,CAACD,GAAD,CAAR;AACD,SAFD,MAEO,IACLC,QAAQ,IAAI,IAAZ,IACA,OAAOA,QAAP,KAAoB,QADpB,IAEAA,QAAQ,CAAC3B,cAAT,CAAwB,SAAxB,CAHK,EAIL;AACA2B,UAAAA,QAAQ,CAACC,OAAT,GAAmBF,GAAnB;AACD;AACF,OA5b4B;;AAAA,YA8b7BF,0BA9b6B,GA8bA,YAAM;AACjC,YAAI,MAAKtD,0BAAL,KAAoC,IAAxC,EAA8C;AAC5CjE,UAAAA,aAAa,CAAC,MAAKiE,0BAAN,CAAb;AACD;;AAED,cAAKA,0BAAL,GAAkC7D,cAAc,CAC9C,MAAKwH,iBADyC,EAE9C1F,gCAF8C,CAAhD;AAID,OAvc4B;;AAAA,YAyc7B0F,iBAzc6B,GAycT,YAAM;AACxB,cAAK3D,0BAAL,GAAkC,IAAlC;;AAEA,cAAK+C,QAAL,CAAc;AAAE3C,UAAAA,WAAW,EAAE;AAAf,SAAd,EAAsC,YAAM;AAC1C;AACA;AACA,gBAAKwB,kBAAL,CAAwB,CAAC,CAAzB,EAA4B,IAA5B;AACD,SAJD;AAKD,OAjd4B;;AAAA;AAE5B;;AA7BH,SA+BSgC,wBA/BT,GA+BE,kCACEC,SADF,EAEEb,SAFF,EAGwB;AACtBc,MAAAA,qBAAmB,CAACD,SAAD,EAAYb,SAAZ,CAAnB;AACAnD,MAAAA,aAAa,CAACgE,SAAD,CAAb;AACA,aAAO,IAAP;AACD,KAtCH;;AAAA;;AAAA,WAwCEE,QAxCF,GAwCE,kBAASoF,YAAT,EAAqC;AACnCA,MAAAA,YAAY,GAAGjG,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYgG,YAAZ,CAAf;AAEA,WAAKpG,QAAL,CAAc,UAAAC,SAAS,EAAI;AACzB,YAAIA,SAAS,CAACmG,YAAV,KAA2BA,YAA/B,EAA6C;AAC3C,iBAAO,IAAP;AACD;;AACD,eAAO;AACLkB,UAAAA,eAAe,EACbrH,SAAS,CAACmG,YAAV,GAAyBA,YAAzB,GAAwC,SAAxC,GAAoD,UAFjD;AAGLA,UAAAA,YAAY,EAAEA,YAHT;AAIL1I,UAAAA,wBAAwB,EAAE;AAJrB,SAAP;AAMD,OAVD,EAUG,KAAK6C,0BAVR;AAWD,KAtDH;;AAAA,WAwDEU,YAxDF,GAwDE,sBAAaiE,KAAb,EAA4BhE,KAA5B,EAAiE;AAAA,UAArCA,KAAqC;AAArCA,QAAAA,KAAqC,GAAd,MAAc;AAAA;;AAC/D,yBAA8B,KAAKnE,KAAnC;AAAA,UAAQkJ,SAAR,gBAAQA,SAAR;AAAA,UAAmB2B,MAAnB,gBAAmBA,MAAnB;AACA,UAAQxB,YAAR,GAAyB,KAAKjJ,KAA9B,CAAQiJ,YAAR;AAEAlB,MAAAA,KAAK,GAAG/E,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAAZ,CAAR,CAJ+D;AAO/D;AACA;;AACA,UAAI5E,aAAa,GAAG,CAApB;;AACA,UAAI,KAAKnE,SAAT,EAAoB;AAClB,YAAMwD,QAAQ,GAAK,KAAKxD,SAAxB;;AACA,YAAI0K,MAAM,KAAK,UAAf,EAA2B;AACzBvG,UAAAA,aAAa,GACXX,QAAQ,CAACX,WAAT,GAAuBW,QAAQ,CAAClG,WAAhC,GACIZ,gBAAgB,EADpB,GAEI,CAHN;AAID,SALD,MAKO;AACLyH,UAAAA,aAAa,GACXX,QAAQ,CAACZ,YAAT,GAAwBY,QAAQ,CAACb,YAAjC,GACIjG,gBAAgB,EADpB,GAEI,CAHN;AAID;AACF;;AAED,WAAKoH,QAAL,CACEmF,6BAA6B,CAC3B,KAAKpJ,KADsB,EAE3BmI,KAF2B,EAG3BhE,KAH2B,EAI3BkF,YAJ2B,EAK3B,KAAKpJ,cALsB,EAM3BqE,aAN2B,CAD/B;AAUD,KA3FH;;AAAA,WA6FEK,iBA7FF,GA6FE,6BAAoB;AAClB,yBAAmD,KAAK3E,KAAxD;AAAA,UAAQjC,SAAR,gBAAQA,SAAR;AAAA,UAAmByM,mBAAnB,gBAAmBA,mBAAnB;AAAA,UAAwCK,MAAxC,gBAAwCA,MAAxC;;AAEA,UAAI,OAAOL,mBAAP,KAA+B,QAA/B,IAA2C,KAAKrK,SAAL,IAAkB,IAAjE,EAAuE;AACrE,YAAMwD,QAAQ,GAAK,KAAKxD,SAAxB,CADqE;;AAGrE,YAAIpC,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA7C,EAA2D;AACzDlH,UAAAA,QAAQ,CAACzF,UAAT,GAAsBsM,mBAAtB;AACD,SAFD,MAEO;AACL7G,UAAAA,QAAQ,CAAClD,SAAT,GAAqB+J,mBAArB;AACD;AACF;;AAED,WAAK5F,mBAAL;AACD,KA3GH;;AAAA,WA6GEC,kBA7GF,GA6GE,8BAAqB;AACnB,yBAA8B,KAAK7E,KAAnC;AAAA,UAAQjC,SAAR,gBAAQA,SAAR;AAAA,UAAmB8M,MAAnB,gBAAmBA,MAAnB;AACA,wBAAmD,KAAKzK,KAAxD;AAAA,UAAQiJ,YAAR,eAAQA,YAAR;AAAA,UAAsB1I,wBAAtB,eAAsBA,wBAAtB;;AAEA,UAAIA,wBAAwB,IAAI,KAAKR,SAAL,IAAkB,IAAlD,EAAwD;AACtD,YAAMwD,QAAQ,GAAK,KAAKxD,SAAxB,CADsD;;AAItD,YAAIpC,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA7C,EAA2D;AACzD,cAAI9M,SAAS,KAAK,KAAlB,EAAyB;AACvB;AACA;AACA;AACA,oBAAQH,gBAAgB,EAAxB;AACE,mBAAK,UAAL;AACE+F,gBAAAA,QAAQ,CAACzF,UAAT,GAAsB,CAACmL,YAAvB;AACA;;AACF,mBAAK,oBAAL;AACE1F,gBAAAA,QAAQ,CAACzF,UAAT,GAAsBmL,YAAtB;AACA;;AACF;AACE,oBAAQ5L,WAAR,GAAqCkG,QAArC,CAAQlG,WAAR;AAAA,oBAAqBuF,WAArB,GAAqCW,QAArC,CAAqBX,WAArB;AACAW,gBAAAA,QAAQ,CAACzF,UAAT,GAAsB8E,WAAW,GAAGvF,WAAd,GAA4B4L,YAAlD;AACA;AAVJ;AAYD,WAhBD,MAgBO;AACL1F,YAAAA,QAAQ,CAACzF,UAAT,GAAsBmL,YAAtB;AACD;AACF,SApBD,MAoBO;AACL1F,UAAAA,QAAQ,CAAClD,SAAT,GAAqB4I,YAArB;AACD;AACF;;AAED,WAAKzE,mBAAL;AACD,KA/IH;;AAAA,WAiJEE,oBAjJF,GAiJE,gCAAuB;AACrB,UAAI,KAAK5E,0BAAL,KAAoC,IAAxC,EAA8C;AAC5CjE,QAAAA,aAAa,CAAC,KAAKiE,0BAAN,CAAb;AACD;AACF,KArJH;;AAAA,WAuJE6E,MAvJF,GAuJE,kBAAS;AACP,yBAiBI,KAAK/E,KAjBT;AAAA,UACEgF,QADF,gBACEA,QADF;AAAA,UAEEC,SAFF,gBAEEA,SAFF;AAAA,UAGElH,SAHF,gBAGEA,SAHF;AAAA,UAIEX,MAJF,gBAIEA,MAJF;AAAA,UAKE8H,QALF,gBAKEA,QALF;AAAA,UAMEC,gBANF,gBAMEA,gBANF;AAAA,UAOEC,YAPF,gBAOEA,YAPF;AAAA,UAQE8D,SARF,gBAQEA,SARF;AAAA,UASE7D,QATF,gBASEA,QATF;AAAA,8CAUEC,OAVF;AAAA,UAUEA,OAVF,qCAUYlH,gBAVZ;AAAA,UAWEyM,MAXF,gBAWEA,MAXF;AAAA,UAYEtF,gBAZF,gBAYEA,gBAZF;AAAA,UAaEC,YAbF,gBAaEA,YAbF;AAAA,UAcEtI,KAdF,gBAcEA,KAdF;AAAA,UAeEuI,cAfF,gBAeEA,cAfF;AAAA,UAgBEtI,KAhBF,gBAgBEA,KAhBF;AAkBA,UAAQmD,WAAR,GAAwB,KAAKF,KAA7B,CAAQE,WAAR,CAnBO;;AAsBP,UAAMwK,YAAY,GAChB/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAD3C;AAGA,UAAMpJ,QAAQ,GAAGqJ,YAAY,GACzB,KAAKE,mBADoB,GAEzB,KAAKC,iBAFT;;AAIA,kCAAgC,KAAKC,iBAAL,EAAhC;AAAA,UAAOzE,UAAP;AAAA,UAAmBC,SAAnB;;AAEA,UAAMV,KAAK,GAAG,EAAd;;AACA,UAAIkD,SAAS,GAAG,CAAhB,EAAmB;AACjB,aAAK,IAAIf,MAAK,GAAG1B,UAAjB,EAA6B0B,MAAK,IAAIzB,SAAtC,EAAiDyB,MAAK,EAAtD,EAA0D;AACxDnC,UAAAA,KAAK,CAACC,IAAN,CACEhJ,aAAa,CAAC+H,QAAD,EAAW;AACtB1G,YAAAA,IAAI,EAAE+G,QADgB;AAEtBtD,YAAAA,GAAG,EAAEuD,OAAO,CAAC6C,MAAD,EAAQ9C,QAAR,CAFU;AAGtB8C,YAAAA,KAAK,EAALA,MAHsB;AAItB7H,YAAAA,WAAW,EAAEmF,cAAc,GAAGnF,WAAH,GAAiB+B,SAJtB;AAKtBnF,YAAAA,KAAK,EAAE,KAAKwE,aAAL,CAAmByG,MAAnB;AALe,WAAX,CADf;AASD;AACF,OA5CM;AA+CP;;;AACA,UAAMmB,kBAAkB,GAAGa,qBAAqB,CAC9C,KAAKnK,KADyC,EAE9C,KAAKC,cAFyC,CAAhD;AAKA,aAAOhD,aAAa,CAClBsI,gBAAgB,IAAIC,YAApB,IAAoC,KADlB,EAElB;AACEP,QAAAA,SAAS,EAATA,SADF;AAEExD,QAAAA,QAAQ,EAARA,QAFF;AAGEiC,QAAAA,GAAG,EAAE,KAAKD,eAHZ;AAIEvG,QAAAA,KAAK;AACHiF,UAAAA,QAAQ,EAAE,UADP;AAEH/E,UAAAA,MAAM,EAANA,MAFG;AAGHD,UAAAA,KAAK,EAALA,KAHG;AAIHE,UAAAA,QAAQ,EAAE,MAJP;AAKH6I,UAAAA,uBAAuB,EAAE,OALtB;AAMHC,UAAAA,UAAU,EAAE,WANT;AAOHpI,UAAAA,SAAS,EAATA;AAPG,WAQAb,KARA;AAJP,OAFkB,EAiBlBD,aAAa,CAACkI,gBAAgB,IAAIC,YAApB,IAAoC,KAArC,EAA4C;AACvDJ,QAAAA,QAAQ,EAAEgB,KAD6C;AAEvDtC,QAAAA,GAAG,EAAEwB,QAFkD;AAGvDhI,QAAAA,KAAK,EAAE;AACLE,UAAAA,MAAM,EAAE0N,YAAY,GAAG,MAAH,GAAYxB,kBAD3B;AAELlD,UAAAA,aAAa,EAAE9F,WAAW,GAAG,MAAH,GAAY+B,SAFjC;AAGLlF,UAAAA,KAAK,EAAE2N,YAAY,GAAGxB,kBAAH,GAAwB;AAHtC;AAHgD,OAA5C,CAjBK,CAApB;AA2BD,KAvOH;;AAAA,WAgRE1E,mBAhRF,GAgRE,+BAAsB;AACpB,UAAI,OAAO,KAAK5E,KAAL,CAAWuB,eAAlB,KAAsC,UAA1C,EAAsD;AACpD,YAAQ2H,SAAR,GAAsB,KAAKlJ,KAA3B,CAAQkJ,SAAR;;AACA,YAAIA,SAAS,GAAG,CAAhB,EAAmB;AACjB,uCAKI,KAAKgC,iBAAL,EALJ;AAAA,cACET,mBADF;AAAA,cAEEC,kBAFF;AAAA,cAGEC,kBAHF;AAAA,cAIEC,iBAJF;;AAMA,eAAK/J,oBAAL,CACE4J,mBADF,EAEEC,kBAFF,EAGEC,kBAHF,EAIEC,iBAJF;AAMD;AACF;;AAED,UAAI,OAAO,KAAK5K,KAAL,CAAWyB,QAAlB,KAA+B,UAAnC,EAA+C;AAC7C,2BAII,KAAKrB,KAJT;AAAA,YACEmK,gBADF,gBACEA,eADF;AAAA,YAEElB,aAFF,gBAEEA,YAFF;AAAA,YAGE1I,yBAHF,gBAGEA,wBAHF;;AAKA,aAAKa,aAAL,CACE+I,gBADF,EAEElB,aAFF,EAGE1I,yBAHF;AAKD;AACF,KA/SH;AAkTE;AACA;AACA;AApTF;;AAAA,WA4VEuK,iBA5VF,GA4VE,6BAAsD;AACpD,yBAAqC,KAAKlL,KAA1C;AAAA,UAAQkJ,SAAR,gBAAQA,SAAR;AAAA,UAAmB3C,aAAnB,gBAAmBA,aAAnB;AACA,yBAAuD,KAAKnG,KAA5D;AAAA,UAAQE,WAAR,gBAAQA,WAAR;AAAA,UAAqBiK,eAArB,gBAAqBA,eAArB;AAAA,UAAsClB,YAAtC,gBAAsCA,YAAtC;;AAEA,UAAIH,SAAS,KAAK,CAAlB,EAAqB;AACnB,eAAO,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,CAAP;AACD;;AAED,UAAMzC,UAAU,GAAG4D,sBAAsB,CACvC,KAAKrK,KADkC,EAEvCqJ,YAFuC,EAGvC,KAAKpJ,cAHkC,CAAzC;AAKA,UAAMyG,SAAS,GAAG4D,yBAAyB,CACzC,KAAKtK,KADoC,EAEzCyG,UAFyC,EAGzC4C,YAHyC,EAIzC,KAAKpJ,cAJoC,CAA3C,CAboD;AAqBpD;;AACA,UAAM0G,gBAAgB,GACpB,CAACrG,WAAD,IAAgBiK,eAAe,KAAK,UAApC,GACInH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkD,aAAZ,CADJ,GAEI,CAHN;AAIA,UAAMK,eAAe,GACnB,CAACtG,WAAD,IAAgBiK,eAAe,KAAK,SAApC,GACInH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkD,aAAZ,CADJ,GAEI,CAHN;AAKA,aAAO,CACLnD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYoD,UAAU,GAAGE,gBAAzB,CADK,EAELvD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYD,IAAI,CAACE,GAAL,CAAS4F,SAAS,GAAG,CAArB,EAAwBxC,SAAS,GAAGE,eAApC,CAAZ,CAFK,EAGLH,UAHK,EAILC,SAJK,CAAP;AAMD,KAjYH;;AAAA;AAAA,IAA6BK,aAA7B,UAKSC,YALT,GAKwB;AACpBjJ,IAAAA,SAAS,EAAE,KADS;AAEpBsH,IAAAA,QAAQ,EAAEhD,SAFU;AAGpBwI,IAAAA,MAAM,EAAE,UAHY;AAIpBtE,IAAAA,aAAa,EAAE,CAJK;AAKpBd,IAAAA,cAAc,EAAE;AALI,GALxB;AA8eD;AAGD;AACA;AACA;AACA;;AAEA,IAAMzB,qBAAmB,GAAG,SAAtBA,mBAAsB,eAWjB;AAAA,MATPgB,QASO,SATPA,QASO;AAAA,MARPjH,SAQO,SARPA,SAQO;AAAA,MAPPX,MAOO,SAPPA,MAOO;AAAA,MANPyN,MAMO,SANPA,MAMO;AAAA,MALPzF,YAKO,SALPA,YAKO;AAAA,MAJPI,YAIO,SAJPA,YAIO;AAAA,MAHPrI,KAGO,SAHPA,KAGO;AAAA,MADPkD,QACO,SADPA,QACO;;AACT,MAAI1B,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,QAAIuG,YAAY,IAAI,IAAhB,IAAwBI,YAAY,IAAI,IAA5C,EAAkD;AAChD,UAAI9G,oBAAkB,IAAI,CAACA,oBAAkB,CAACuI,GAAnB,CAAuB5G,QAAvB,CAA3B,EAA6D;AAC3D3B,QAAAA,oBAAkB,CAACwI,GAAnB,CAAuB7G,QAAvB;AACA8G,QAAAA,OAAO,CAACC,IAAR,CACE,mEACE,qEAFJ;AAID;AACF,KATwC;;;AAYzC,QAAM0D,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;;AAEA,YAAQ9M,SAAR;AACE,WAAK,YAAL;AACA,WAAK,UAAL;AACE,YAAIiM,oBAAoB,IAAI,CAACA,oBAAoB,CAAC/C,GAArB,CAAyB5G,QAAzB,CAA7B,EAAiE;AAC/D2J,UAAAA,oBAAoB,CAAC9C,GAArB,CAAyB7G,QAAzB;AACA8G,UAAAA,OAAO,CAACC,IAAR,CACE,mEACE,yFAFJ;AAID;;AACD;;AACF,WAAK,KAAL;AACA,WAAK,KAAL;AACE;AACA;;AACF;AACE,cAAMC,KAAK,CACT,qDACE,yCADF,WAEMtJ,SAFN,uBADS,CAAX;AAhBJ;;AAuBA,YAAQ8M,MAAR;AACE,WAAK,YAAL;AACA,WAAK,UAAL;AACE;AACA;;AACF;AACE,cAAMxD,KAAK,CACT,kDACE,qDADF,WAEMwD,MAFN,uBADS,CAAX;AANJ;;AAaA,QAAI7F,QAAQ,IAAI,IAAhB,EAAsB;AACpB,YAAMqC,KAAK,CACT,oDACE,qCADF,YAEMrC,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;AAKD;;AAED,QAAI8F,YAAY,IAAI,OAAO3N,KAAP,KAAiB,QAArC,EAA+C;AAC7C,YAAMkK,KAAK,CACT,iDACE,oDADF,YAEMlK,KAAK,KAAK,IAAV,GAAiB,MAAjB,GAA0B,OAAOA,KAFvC,wBADS,CAAX;AAKD,KAND,MAMO,IAAI,CAAC2N,YAAD,IAAiB,OAAO1N,MAAP,KAAkB,QAAvC,EAAiD;AACtD,YAAMiK,KAAK,CACT,kDACE,mDADF,YAEMjK,MAAM,KAAK,IAAX,GAAkB,MAAlB,GAA2B,OAAOA,MAFxC,wBADS,CAAX;AAKD;AACF;AACF,CApFD;;AC9oBA,IAAMkK,6BAA2B,GAAG,EAApC;;AAmBA,IAAMW,iBAAe,GAAG,SAAlBA,eAAkB,CACtBjI,KADsB,EAEtBmI,KAFsB,EAGtBC,aAHsB,EAIL;AACjB,aAAuBpI,KAAvB;AAAA,MAAQsI,QAAR,QAAQA,QAAR;AACA,MAAQD,eAAR,GAA+CD,aAA/C,CAAQC,eAAR;AAAA,MAAyBE,iBAAzB,GAA+CH,aAA/C,CAAyBG,iBAAzB;;AAEA,MAAIJ,KAAK,GAAGI,iBAAZ,EAA+B;AAC7B,QAAItG,MAAM,GAAG,CAAb;;AACA,QAAIsG,iBAAiB,IAAI,CAAzB,EAA4B;AAC1B,UAAMZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;AACAtG,MAAAA,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA5C;AACD;;AAED,SAAK,IAAI4L,CAAC,GAAGD,iBAAiB,GAAG,CAAjC,EAAoCC,CAAC,IAAIL,KAAzC,EAAgDK,CAAC,EAAjD,EAAqD;AACnD,UAAI5L,IAAI,GAAK0L,QAAF,CAAkCE,CAAlC,CAAX;AAEAH,MAAAA,eAAe,CAACG,CAAD,CAAf,GAAqB;AACnBvG,QAAAA,MAAM,EAANA,MADmB;AAEnBrF,QAAAA,IAAI,EAAJA;AAFmB,OAArB;AAKAqF,MAAAA,MAAM,IAAIrF,IAAV;AACD;;AAEDwL,IAAAA,aAAa,CAACG,iBAAd,GAAkCJ,KAAlC;AACD;;AAED,SAAOE,eAAe,CAACF,KAAD,CAAtB;AACD,CA9BD;;AAgCA,IAAMM,iBAAe,GAAG,SAAlBA,eAAkB,CACtBzI,KADsB,EAEtBoI,aAFsB,EAGtBnG,MAHsB,EAInB;AACH,MAAQoG,eAAR,GAA+CD,aAA/C,CAAQC,eAAR;AAAA,MAAyBE,iBAAzB,GAA+CH,aAA/C,CAAyBG,iBAAzB;AAEA,MAAMG,sBAAsB,GAC1BH,iBAAiB,GAAG,CAApB,GAAwBF,eAAe,CAACE,iBAAD,CAAf,CAAmCtG,MAA3D,GAAoE,CADtE;;AAGA,MAAIyG,sBAAsB,IAAIzG,MAA9B,EAAsC;AACpC;AACA,WAAO0G,6BAA2B,CAChC3I,KADgC,EAEhCoI,aAFgC,EAGhCG,iBAHgC,EAIhC,CAJgC,EAKhCtG,MALgC,CAAlC;AAOD,GATD,MASO;AACL;AACA;AACA;AACA,WAAO2G,kCAAgC,CACrC5I,KADqC,EAErCoI,aAFqC,EAGrChF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYkF,iBAAZ,CAHqC,EAIrCtG,MAJqC,CAAvC;AAMD;AACF,CA9BD;;AAgCA,IAAM0G,6BAA2B,GAAG,SAA9BA,2BAA8B,CAClC3I,KADkC,EAElCoI,aAFkC,EAGlCS,IAHkC,EAIlCC,GAJkC,EAKlC7G,MALkC,EAMvB;AACX,SAAO6G,GAAG,IAAID,IAAd,EAAoB;AAClB,QAAME,MAAM,GAAGD,GAAG,GAAG1F,IAAI,CAAC4F,KAAL,CAAW,CAACH,IAAI,GAAGC,GAAR,IAAe,CAA1B,CAArB;AACA,QAAMG,aAAa,GAAGhB,iBAAe,CAACjI,KAAD,EAAQ+I,MAAR,EAAgBX,aAAhB,CAAf,CAA8CnG,MAApE;;AAEA,QAAIgH,aAAa,KAAKhH,MAAtB,EAA8B;AAC5B,aAAO8G,MAAP;AACD,KAFD,MAEO,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;AACjC6G,MAAAA,GAAG,GAAGC,MAAM,GAAG,CAAf;AACD,KAFM,MAEA,IAAIE,aAAa,GAAGhH,MAApB,EAA4B;AACjC4G,MAAAA,IAAI,GAAGE,MAAM,GAAG,CAAhB;AACD;AACF;;AAED,MAAID,GAAG,GAAG,CAAV,EAAa;AACX,WAAOA,GAAG,GAAG,CAAb;AACD,GAFD,MAEO;AACL,WAAO,CAAP;AACD;AACF,CAzBD;;AA2BA,IAAMF,kCAAgC,GAAG,SAAnCA,gCAAmC,CACvC5I,KADuC,EAEvCoI,aAFuC,EAGvCD,KAHuC,EAIvClG,MAJuC,EAK5B;AACX,MAAQiH,SAAR,GAAsBlJ,KAAtB,CAAQkJ,SAAR;AACA,MAAIC,QAAQ,GAAG,CAAf;;AAEA,SACEhB,KAAK,GAAGe,SAAR,IACAjB,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAAf,CAA6CnG,MAA7C,GAAsDA,MAFxD,EAGE;AACAkG,IAAAA,KAAK,IAAIgB,QAAT;AACAA,IAAAA,QAAQ,IAAI,CAAZ;AACD;;AAED,SAAOR,6BAA2B,CAChC3I,KADgC,EAEhCoI,aAFgC,EAGhChF,IAAI,CAACE,GAAL,CAAS6E,KAAT,EAAgBe,SAAS,GAAG,CAA5B,CAHgC,EAIhC9F,IAAI,CAAC4F,KAAL,CAAWb,KAAK,GAAG,CAAnB,CAJgC,EAKhClG,MALgC,CAAlC;AAOD,CAxBD;;AA0BA,IAAMkI,qBAAqB,GAAG,SAAxBA,qBAAwB,eAGzB;AAAA,MAFDjB,SAEC,SAFDA,SAEC;AAAA,MADDb,eACC,SADDA,eACC;AAAA,MADgB8C,iBAChB,SADgBA,iBAChB;AAAA,MADmC5C,iBACnC,SADmCA,iBACnC;AACH,MAAI6C,wBAAwB,GAAG,CAA/B,CADG;AAIH;;AACA,MAAI7C,iBAAiB,IAAIW,SAAzB,EAAoC;AAClCX,IAAAA,iBAAiB,GAAGW,SAAS,GAAG,CAAhC;AACD;;AAED,MAAIX,iBAAiB,IAAI,CAAzB,EAA4B;AAC1B,QAAMZ,YAAY,GAAGU,eAAe,CAACE,iBAAD,CAApC;AACA6C,IAAAA,wBAAwB,GAAGzD,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAA9D;AACD;;AAED,MAAMgL,kBAAkB,GAAGsB,SAAS,GAAGX,iBAAZ,GAAgC,CAA3D;AACA,MAAMV,0BAA0B,GAAGD,kBAAkB,GAAGuD,iBAAxD;AAEA,SAAOC,wBAAwB,GAAGvD,0BAAlC;AACD,CArBD;;AAuBA,IAAMwD,gBAAgB,gBAAGpB,mBAAmB,CAAC;AAC3CC,EAAAA,aAAa,EAAE,uBACblK,KADa,EAEbmI,KAFa,EAGbC,aAHa;AAAA,WAIFH,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAAf,CAA6CnG,MAJ3C;AAAA,GAD4B;AAO3CmI,EAAAA,WAAW,EAAE,qBACXpK,KADW,EAEXmI,KAFW,EAGXC,aAHW;AAAA,WAIAA,aAAa,CAACC,eAAd,CAA8BF,KAA9B,EAAqCvL,IAJrC;AAAA,GAP8B;AAa3CuN,EAAAA,qBAAqB,EAArBA,qBAb2C;AAe3Cf,EAAAA,6BAA6B,EAAE,uCAC7BpJ,KAD6B,EAE7BmI,KAF6B,EAG7BhE,KAH6B,EAI7BkF,YAJ6B,EAK7BjB,aAL6B,EAM7B9D,aAN6B,EAOlB;AACX,QAAQvG,SAAR,GAA6CiC,KAA7C,CAAQjC,SAAR;AAAA,QAAmBX,MAAnB,GAA6C4C,KAA7C,CAAmB5C,MAAnB;AAAA,QAA2ByN,MAA3B,GAA6C7K,KAA7C,CAA2B6K,MAA3B;AAAA,QAAmC1N,KAAnC,GAA6C6C,KAA7C,CAAmC7C,KAAnC,CADW;;AAIX,QAAM2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;AACA,QAAMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;AACA,QAAMuK,YAAY,GAAGM,iBAAe,CAACjI,KAAD,EAAQmI,KAAR,EAAeC,aAAf,CAApC,CANW;AASX;;AACA,QAAMkB,kBAAkB,GAAGa,qBAAqB,CAACnK,KAAD,EAAQoI,aAAR,CAAhD;AAEA,QAAMmB,SAAS,GAAGnG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBD,IAAI,CAACE,GAAL,CAASgG,kBAAkB,GAAG1M,IAA9B,EAAoC+K,YAAY,CAAC1F,MAAjD,CAFgB,CAAlB;AAIA,QAAMuH,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBsE,YAAY,CAAC1F,MAAb,GAAsBrF,IAAtB,GAA6B+K,YAAY,CAAC/K,IAA1C,GAAiD0H,aAFjC,CAAlB;;AAKA,QAAIH,KAAK,KAAK,OAAd,EAAuB;AACrB,UACEkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IACAyM,YAAY,IAAIE,SAAS,GAAG3M,IAF9B,EAGE;AACAuH,QAAAA,KAAK,GAAG,MAAR;AACD,OALD,MAKO;AACLA,QAAAA,KAAK,GAAG,QAAR;AACD;AACF;;AAED,YAAQA,KAAR;AACE,WAAK,OAAL;AACE,eAAOoF,SAAP;;AACF,WAAK,KAAL;AACE,eAAOC,SAAP;;AACF,WAAK,QAAL;AACE,eAAOpG,IAAI,CAACqG,KAAL,CAAWD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CAAjD,CAAP;;AACF,WAAK,MAAL;AACA;AACE,YAAIH,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;AAC1D,iBAAOF,YAAP;AACD,SAFD,MAEO,IAAIA,YAAY,GAAGG,SAAnB,EAA8B;AACnC,iBAAOA,SAAP;AACD,SAFM,MAEA;AACL,iBAAOD,SAAP;AACD;;AAfL;AAiBD,GAvE0C;AAyE3Cc,EAAAA,sBAAsB,EAAE,gCACtBrK,KADsB,EAEtBiC,MAFsB,EAGtBmG,aAHsB;AAAA,WAIXK,iBAAe,CAACzI,KAAD,EAAQoI,aAAR,EAAuBnG,MAAvB,CAJJ;AAAA,GAzEmB;AA+E3CqI,EAAAA,yBAAyB,EAAE,mCACzBtK,KADyB,EAEzByG,UAFyB,EAGzB4C,YAHyB,EAIzBjB,aAJyB,EAKd;AACX,QAAQrK,SAAR,GAAwDiC,KAAxD,CAAQjC,SAAR;AAAA,QAAmBX,MAAnB,GAAwD4C,KAAxD,CAAmB5C,MAAnB;AAAA,QAA2B8L,SAA3B,GAAwDlJ,KAAxD,CAA2BkJ,SAA3B;AAAA,QAAsC2B,MAAtC,GAAwD7K,KAAxD,CAAsC6K,MAAtC;AAAA,QAA8C1N,KAA9C,GAAwD6C,KAAxD,CAA8C7C,KAA9C,CADW;;AAIX,QAAM2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;AACA,QAAMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;AACA,QAAMuK,YAAY,GAAGM,iBAAe,CAACjI,KAAD,EAAQyG,UAAR,EAAoB2B,aAApB,CAApC;AACA,QAAMmB,SAAS,GAAGF,YAAY,GAAGzM,IAAjC;AAEA,QAAIqF,MAAM,GAAG0F,YAAY,CAAC1F,MAAb,GAAsB0F,YAAY,CAAC/K,IAAhD;AACA,QAAI8J,SAAS,GAAGD,UAAhB;;AAEA,WAAOC,SAAS,GAAGwC,SAAS,GAAG,CAAxB,IAA6BjH,MAAM,GAAGsH,SAA7C,EAAwD;AACtD7C,MAAAA,SAAS;AACTzE,MAAAA,MAAM,IAAIgG,iBAAe,CAACjI,KAAD,EAAQ0G,SAAR,EAAmB0B,aAAnB,CAAf,CAAiDxL,IAA3D;AACD;;AAED,WAAO8J,SAAP;AACD,GAtG0C;AAwG3C7G,EAAAA,iBAxG2C,6BAwGzBG,KAxGyB,EAwGNK,QAxGM,EAwGwB;AACjE,gBAAgCL,KAAhC;AAAA,QAAQmL,iBAAR,SAAQA,iBAAR;AAEA,QAAM/C,aAAa,GAAG;AACpBC,MAAAA,eAAe,EAAE,EADG;AAEpB8C,MAAAA,iBAAiB,EAAEA,iBAAiB,IAAI7D,6BAFpB;AAGpBiB,MAAAA,iBAAiB,EAAE,CAAC;AAHA,KAAtB;;AAMAlI,IAAAA,QAAQ,CAACiL,eAAT,GAA2B,UACzBnD,KADyB,EAEzByB,iBAFyB,EAGtB;AAAA,UADHA,iBACG;AADHA,QAAAA,iBACG,GAD2B,IAC3B;AAAA;;AACHxB,MAAAA,aAAa,CAACG,iBAAd,GAAkCnF,IAAI,CAACE,GAAL,CAChC8E,aAAa,CAACG,iBADkB,EAEhCJ,KAAK,GAAG,CAFwB,CAAlC,CADG;AAOH;AACA;AACA;;AACA9H,MAAAA,QAAQ,CAACyB,kBAAT,CAA4B,CAAC,CAA7B;;AAEA,UAAI8H,iBAAJ,EAAuB;AACrBvJ,QAAAA,QAAQ,CAAC0J,WAAT;AACD;AACF,KAlBD;;AAoBA,WAAO3B,aAAP;AACD,GAtI0C;AAwI3CtI,EAAAA,qCAAqC,EAAE,KAxII;AA0I3CC,EAAAA,aAAa,EAAE,8BAAoC;AAAA,QAAjCuI,QAAiC,SAAjCA,QAAiC;;AACjD,QAAI3J,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAI,OAAOyJ,QAAP,KAAoB,UAAxB,EAAoC;AAClC,cAAMjB,KAAK,CACT,oDACE,8BADF,YAEMiB,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;AAKD;AACF;AACF;AApJ0C,CAAD,CAA5C;;AC/JA,IAAMiD,aAAa,gBAAGvM,mBAAmB,CAAC;AACxCC,EAAAA,eAAe,EAAE,+BAA8BkJ,KAA9B;AAAA,QAAGxG,WAAH,QAAGA,WAAH;AAAA,WACfwG,KAAK,GAAKxG,WADK;AAAA,GADuB;AAIxCvC,EAAAA,cAAc,EAAE,+BAA8B+I,KAA9B;AAAA,QAAGxG,WAAH,SAAGA,WAAH;AAAA,WACZA,WADY;AAAA,GAJwB;AAOxCjC,EAAAA,YAAY,EAAE,6BAA4ByI,KAA5B;AAAA,QAAGvG,SAAH,SAAGA,SAAH;AAAA,WACZuG,KAAK,GAAKvG,SADE;AAAA,GAP0B;AAUxCnC,EAAAA,YAAY,EAAE,6BAA4B0I,KAA5B;AAAA,QAAGvG,SAAH,SAAGA,SAAH;AAAA,WACVA,SADU;AAAA,GAV0B;AAaxCvC,EAAAA,uBAAuB,EAAE;AAAA,QAAGgF,QAAH,SAAGA,QAAH;AAAA,QAAazC,SAAb,SAAaA,SAAb;AAAA,WACrBA,SAAF,GAA6ByC,QADN;AAAA,GAbe;AAgBxC/E,EAAAA,sBAAsB,EAAE;AAAA,QAAG8E,WAAH,SAAGA,WAAH;AAAA,QAAgBzC,WAAhB,SAAgBA,WAAhB;AAAA,WACpBA,WAAF,GAA+ByC,WADT;AAAA,GAhBgB;AAmBxC7E,EAAAA,8BAA8B,EAAE,+CAE9BlB,WAF8B,EAG9B8F,KAH8B,EAI9BjG,UAJ8B,EAK9BkK,aAL8B,EAM9B9D,aAN8B,EAOnB;AAAA,QANTF,WAMS,SANTA,WAMS;AAAA,QANIzC,WAMJ,SANIA,WAMJ;AAAA,QANiBxE,KAMjB,SANiBA,KAMjB;AACX,QAAMqO,gBAAgB,GAAGpI,IAAI,CAACC,GAAL,CACvB,CADuB,EAEvBe,WAAW,GAAKzC,WAAhB,GAA6CxE,KAFtB,CAAzB;AAIA,QAAMoM,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChBkI,gBADgB,EAEhBnN,WAAW,GAAKsD,WAFA,CAAlB;AAIA,QAAM6H,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhBhF,WAAW,GAAKsD,WAAhB,GACExE,KADF,GAEEmH,aAFF,GAGI3C,WALY,CAAlB;;AAQA,QAAIwC,KAAK,KAAK,OAAd,EAAuB;AACrB,UAAIjG,UAAU,IAAIsL,SAAS,GAAGrM,KAA1B,IAAmCe,UAAU,IAAIqL,SAAS,GAAGpM,KAAjE,EAAwE;AACtEgH,QAAAA,KAAK,GAAG,MAAR;AACD,OAFD,MAEO;AACLA,QAAAA,KAAK,GAAG,QAAR;AACD;AACF;;AAED,YAAQA,KAAR;AACE,WAAK,OAAL;AACE,eAAOoF,SAAP;;AACF,WAAK,KAAL;AACE,eAAOC,SAAP;;AACF,WAAK,QAAL;AACE;AACA;AACA,YAAMiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;AAGA,YAAIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAUvO,KAAK,GAAG,CAAlB,CAAnB,EAAyC;AACvC,iBAAO,CAAP,CADuC;AAExC,SAFD,MAEO,IAAIsO,YAAY,GAAGD,gBAAgB,GAAGpI,IAAI,CAAC4F,KAAL,CAAW7L,KAAK,GAAG,CAAnB,CAAtC,EAA6D;AAClE,iBAAOqO,gBAAP,CADkE;AAEnE,SAFM,MAEA;AACL,iBAAOC,YAAP;AACD;;AACH,WAAK,MAAL;AACA;AACE,YAAIvN,UAAU,IAAIsL,SAAd,IAA2BtL,UAAU,IAAIqL,SAA7C,EAAwD;AACtD,iBAAOrL,UAAP;AACD,SAFD,MAEO,IAAIsL,SAAS,GAAGD,SAAhB,EAA2B;AAChC;AACA;AACA,iBAAOC,SAAP;AACD,SAJM,MAIA,IAAItL,UAAU,GAAGsL,SAAjB,EAA4B;AACjC,iBAAOA,SAAP;AACD,SAFM,MAEA;AACL,iBAAOD,SAAP;AACD;;AA9BL;AAgCD,GAnFuC;AAqFxC/J,EAAAA,2BAA2B,EAAE,4CAE3BjB,QAF2B,EAG3B4F,KAH2B,EAI3B1D,SAJ2B,EAK3B2H,aAL2B,EAM3B9D,aAN2B,EAOhB;AAAA,QANT1C,SAMS,SANTA,SAMS;AAAA,QANExE,MAMF,SANEA,MAMF;AAAA,QANUiH,QAMV,SANUA,QAMV;AACX,QAAMsH,aAAa,GAAGvI,IAAI,CAACC,GAAL,CACpB,CADoB,EAEpBgB,QAAQ,GAAKzC,SAAb,GAAwCxE,MAFpB,CAAtB;AAIA,QAAMmM,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChBqI,aADgB,EAEhBpN,QAAQ,GAAKqD,SAFG,CAAlB;AAIA,QAAM4H,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhB9E,QAAQ,GAAKqD,SAAb,GACExE,MADF,GAEEkH,aAFF,GAGI1C,SALY,CAAlB;;AAQA,QAAIuC,KAAK,KAAK,OAAd,EAAuB;AACrB,UAAI1D,SAAS,IAAI+I,SAAS,GAAGpM,MAAzB,IAAmCqD,SAAS,IAAI8I,SAAS,GAAGnM,MAAhE,EAAwE;AACtE+G,QAAAA,KAAK,GAAG,MAAR;AACD,OAFD,MAEO;AACLA,QAAAA,KAAK,GAAG,QAAR;AACD;AACF;;AAED,YAAQA,KAAR;AACE,WAAK,OAAL;AACE,eAAOoF,SAAP;;AACF,WAAK,KAAL;AACE,eAAOC,SAAP;;AACF,WAAK,QAAL;AACE;AACA;AACA,YAAMiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;AAGA,YAAIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAUtO,MAAM,GAAG,CAAnB,CAAnB,EAA0C;AACxC,iBAAO,CAAP,CADwC;AAEzC,SAFD,MAEO,IAAIqO,YAAY,GAAGE,aAAa,GAAGvI,IAAI,CAAC4F,KAAL,CAAW5L,MAAM,GAAG,CAApB,CAAnC,EAA2D;AAChE,iBAAOuO,aAAP,CADgE;AAEjE,SAFM,MAEA;AACL,iBAAOF,YAAP;AACD;;AACH,WAAK,MAAL;AACA;AACE,YAAIhL,SAAS,IAAI+I,SAAb,IAA0B/I,SAAS,IAAI8I,SAA3C,EAAsD;AACpD,iBAAO9I,SAAP;AACD,SAFD,MAEO,IAAI+I,SAAS,GAAGD,SAAhB,EAA2B;AAChC;AACA;AACA,iBAAOC,SAAP;AACD,SAJM,MAIA,IAAI/I,SAAS,GAAG+I,SAAhB,EAA2B;AAChC,iBAAOA,SAAP;AACD,SAFM,MAEA;AACL,iBAAOD,SAAP;AACD;;AA9BL;AAgCD,GArJuC;AAuJxCrK,EAAAA,4BAA4B,EAAE,6CAE5BhB,UAF4B;AAAA,QAC1ByD,WAD0B,SAC1BA,WAD0B;AAAA,QACbyC,WADa,SACbA,WADa;AAAA,WAI5BhB,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CACEc,WAAW,GAAG,CADhB,EAEEhB,IAAI,CAAC4F,KAAL,CAAW9K,UAAU,GAAKyD,WAA1B,CAFF,CAFF,CAJ4B;AAAA,GAvJU;AAmKxCxC,EAAAA,+BAA+B,EAAE,iDAE/BsH,UAF+B,EAG/BvI,UAH+B,EAIpB;AAAA,QAHTyD,WAGS,UAHTA,WAGS;AAAA,QAHIyC,WAGJ,UAHIA,WAGJ;AAAA,QAHiBjH,KAGjB,UAHiBA,KAGjB;AACX,QAAMiF,IAAI,GAAGqE,UAAU,GAAK9E,WAA5B;AACA,QAAMiK,iBAAiB,GAAGxI,IAAI,CAACsI,IAAL,CACxB,CAACvO,KAAK,GAAGe,UAAR,GAAqBkE,IAAtB,IAAgCT,WADR,CAA1B;AAGA,WAAOyB,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACEc,WAAW,GAAG,CADhB,EAEEqC,UAAU,GAAGmF,iBAAb,GAAiC,CAFnC;AAAA,KAFK,CAAP;AAOD,GAnLuC;AAqLxCjM,EAAAA,yBAAyB,EAAE,2CAEzBc,SAFyB;AAAA,QACvBmB,SADuB,UACvBA,SADuB;AAAA,QACZyC,QADY,UACZA,QADY;AAAA,WAIzBjB,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CAASe,QAAQ,GAAG,CAApB,EAAuBjB,IAAI,CAAC4F,KAAL,CAAWvI,SAAS,GAAKmB,SAAzB,CAAvB,CAFF,CAJyB;AAAA,GArLa;AA8LxChC,EAAAA,4BAA4B,EAAE,8CAE5B6G,UAF4B,EAG5BhG,SAH4B,EAIjB;AAAA,QAHTmB,SAGS,UAHTA,SAGS;AAAA,QAHEyC,QAGF,UAHEA,QAGF;AAAA,QAHYjH,MAGZ,UAHYA,MAGZ;AACX,QAAMmF,GAAG,GAAGkE,UAAU,GAAK7E,SAA3B;AACA,QAAMiK,cAAc,GAAGzI,IAAI,CAACsI,IAAL,CACrB,CAACtO,MAAM,GAAGqD,SAAT,GAAqB8B,GAAtB,IAA+BX,SADV,CAAvB;AAGA,WAAOwB,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACEe,QAAQ,GAAG,CADb,EAEEoC,UAAU,GAAGoF,cAAb,GAA8B,CAFhC;AAAA,KAFK,CAAP;AAOD,GA9MuC;AAgNxChM,EAAAA,iBAhNwC,6BAgNtBG,KAhNsB,EAgNE;AAEzC,GAlNuC;AAoNxCF,EAAAA,qCAAqC,EAAE,IApNC;AAsNxCC,EAAAA,aAAa,EAAE,+BAAkD;AAAA,QAA/C4B,WAA+C,UAA/CA,WAA+C;AAAA,QAAlCC,SAAkC,UAAlCA,SAAkC;;AAC/D,QAAIjD,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAI,OAAO8C,WAAP,KAAuB,QAA3B,EAAqC;AACnC,cAAM0F,KAAK,CACT,uDACE,4BADF,YAGI1F,WAAW,KAAK,IAAhB,GAAuB,MAAvB,GAAgC,OAAOA,WAH3C,wBADS,CAAX;AAOD;;AAED,UAAI,OAAOC,SAAP,KAAqB,QAAzB,EAAmC;AACjC,cAAMyF,KAAK,CACT,qDACE,4BADF,YAEMzF,SAAS,KAAK,IAAd,GAAqB,MAArB,GAA8B,OAAOA,SAF3C,wBADS,CAAX;AAKD;AACF;AACF;AA1OuC,CAAD,CAAzC;;ACEA,IAAMkK,aAAa,gBAAG7B,mBAAmB,CAAC;AACxCC,EAAAA,aAAa,EAAE,6BAA2B/B,KAA3B;AAAA,QAAGG,QAAH,QAAGA,QAAH;AAAA,WACbH,KAAK,GAAKG,QADG;AAAA,GADyB;AAIxC8B,EAAAA,WAAW,EAAE,4BAA2BjC,KAA3B;AAAA,QAAGG,QAAH,SAAGA,QAAH;AAAA,WACTA,QADS;AAAA,GAJ2B;AAOxC6B,EAAAA,qBAAqB,EAAE;AAAA,QAAGjB,SAAH,SAAGA,SAAH;AAAA,QAAcZ,QAAd,SAAcA,QAAd;AAAA,WACnBA,QAAF,GAA4BY,SADP;AAAA,GAPiB;AAUxCE,EAAAA,6BAA6B,EAAE,8CAE7BjB,KAF6B,EAG7BhE,KAH6B,EAI7BkF,YAJ6B,EAK7BjB,aAL6B,EAM7B9D,aAN6B,EAOlB;AAAA,QANTvG,SAMS,SANTA,SAMS;AAAA,QANEX,MAMF,SANEA,MAMF;AAAA,QANU8L,SAMV,SANUA,SAMV;AAAA,QANqBZ,QAMrB,SANqBA,QAMrB;AAAA,QAN+BuC,MAM/B,SAN+BA,MAM/B;AAAA,QANuC1N,KAMvC,SANuCA,KAMvC;AACX;AACA,QAAM2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;AACA,QAAMjO,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;AACA,QAAM2O,cAAc,GAAG3I,IAAI,CAACC,GAAL,CACrB,CADqB,EAErB6F,SAAS,GAAKZ,QAAd,GAAwC1L,IAFnB,CAAvB;AAIA,QAAM2M,SAAS,GAAGnG,IAAI,CAACE,GAAL,CAChByI,cADgB,EAEhB5D,KAAK,GAAKG,QAFM,CAAlB;AAIA,QAAMkB,SAAS,GAAGpG,IAAI,CAACC,GAAL,CAChB,CADgB,EAEhB8E,KAAK,GAAKG,QAAV,GACE1L,IADF,GAEI0L,QAFJ,GAGEhE,aALc,CAAlB;;AAQA,QAAIH,KAAK,KAAK,OAAd,EAAuB;AACrB,UACEkF,YAAY,IAAIG,SAAS,GAAG5M,IAA5B,IACAyM,YAAY,IAAIE,SAAS,GAAG3M,IAF9B,EAGE;AACAuH,QAAAA,KAAK,GAAG,MAAR;AACD,OALD,MAKO;AACLA,QAAAA,KAAK,GAAG,QAAR;AACD;AACF;;AAED,YAAQA,KAAR;AACE,WAAK,OAAL;AACE,eAAOoF,SAAP;;AACF,WAAK,KAAL;AACE,eAAOC,SAAP;;AACF,WAAK,QAAL;AAAe;AACb;AACA;AACA,cAAMiC,YAAY,GAAGrI,IAAI,CAACqG,KAAL,CACnBD,SAAS,GAAG,CAACD,SAAS,GAAGC,SAAb,IAA0B,CADnB,CAArB;;AAGA,cAAIiC,YAAY,GAAGrI,IAAI,CAACsI,IAAL,CAAU9O,IAAI,GAAG,CAAjB,CAAnB,EAAwC;AACtC,mBAAO,CAAP,CADsC;AAEvC,WAFD,MAEO,IAAI6O,YAAY,GAAGM,cAAc,GAAG3I,IAAI,CAAC4F,KAAL,CAAWpM,IAAI,GAAG,CAAlB,CAApC,EAA0D;AAC/D,mBAAOmP,cAAP,CAD+D;AAEhE,WAFM,MAEA;AACL,mBAAON,YAAP;AACD;AACF;;AACD,WAAK,MAAL;AACA;AACE,YAAIpC,YAAY,IAAIG,SAAhB,IAA6BH,YAAY,IAAIE,SAAjD,EAA4D;AAC1D,iBAAOF,YAAP;AACD,SAFD,MAEO,IAAIA,YAAY,GAAGG,SAAnB,EAA8B;AACnC,iBAAOA,SAAP;AACD,SAFM,MAEA;AACL,iBAAOD,SAAP;AACD;;AA3BL;AA6BD,GA7EuC;AA+ExCc,EAAAA,sBAAsB,EAAE,uCAEtBpI,MAFsB;AAAA,QACpBiH,SADoB,SACpBA,SADoB;AAAA,QACTZ,QADS,SACTA,QADS;AAAA,WAItBlF,IAAI,CAACC,GAAL,CACE,CADF,EAEED,IAAI,CAACE,GAAL,CAAS4F,SAAS,GAAG,CAArB,EAAwB9F,IAAI,CAAC4F,KAAL,CAAW/G,MAAM,GAAKqG,QAAtB,CAAxB,CAFF,CAJsB;AAAA,GA/EgB;AAwFxCgC,EAAAA,yBAAyB,EAAE,0CAEzB7D,UAFyB,EAGzB4C,YAHyB,EAId;AAAA,QAHTtL,SAGS,SAHTA,SAGS;AAAA,QAHEX,MAGF,SAHEA,MAGF;AAAA,QAHU8L,SAGV,SAHUA,SAGV;AAAA,QAHqBZ,QAGrB,SAHqBA,QAGrB;AAAA,QAH+BuC,MAG/B,SAH+BA,MAG/B;AAAA,QAHuC1N,KAGvC,SAHuCA,KAGvC;AACX;AACA,QAAM2N,YAAY,GAAG/M,SAAS,KAAK,YAAd,IAA8B8M,MAAM,KAAK,YAA9D;AACA,QAAM5I,MAAM,GAAGwE,UAAU,GAAK6B,QAA9B;AACA,QAAM1L,IAAI,GAAMkO,YAAY,GAAG3N,KAAH,GAAWC,MAAvC;AACA,QAAM4O,eAAe,GAAG5I,IAAI,CAACsI,IAAL,CACtB,CAAC9O,IAAI,GAAGyM,YAAP,GAAsBpH,MAAvB,IAAmCqG,QADb,CAAxB;AAGA,WAAOlF,IAAI,CAACC,GAAL,CACL,CADK,EAELD,IAAI,CAACE,GAAL,CACE4F,SAAS,GAAG,CADd,EAEEzC,UAAU,GAAGuF,eAAb,GAA+B,CAFjC;AAAA,KAFK,CAAP;AAOD,GA3GuC;AA6GxCnM,EAAAA,iBA7GwC,6BA6GtBG,KA7GsB,EA6GE;AAEzC,GA/GuC;AAiHxCF,EAAAA,qCAAqC,EAAE,IAjHC;AAmHxCC,EAAAA,aAAa,EAAE,8BAAoC;AAAA,QAAjCuI,QAAiC,SAAjCA,QAAiC;;AACjD,QAAI3J,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,YAA7B,EAA2C;AACzC,UAAI,OAAOyJ,QAAP,KAAoB,QAAxB,EAAkC;AAChC,cAAMjB,KAAK,CACT,oDACE,4BADF,YAEMiB,QAAQ,KAAK,IAAb,GAAoB,MAApB,GAA6B,OAAOA,QAF1C,wBADS,CAAX;AAKD;AACF;AACF;AA7HuC,CAAD,CAAzC;;ACNA;AACA;AACA,AAAe,SAAS2D,cAAT,CAAwBC,IAAxB,EAAsCC,IAAtC,EAA6D;AAC1E,OAAK,IAAIC,SAAT,IAAsBF,IAAtB,EAA4B;AAC1B,QAAI,EAAEE,SAAS,IAAID,IAAf,CAAJ,EAA0B;AACxB,aAAO,IAAP;AACD;AACF;;AACD,OAAK,IAAIC,UAAT,IAAsBD,IAAtB,EAA4B;AAC1B,QAAID,IAAI,CAACE,UAAD,CAAJ,KAAoBD,IAAI,CAACC,UAAD,CAA5B,EAAyC;AACvC,aAAO,IAAP;AACD;AACF;;AACD,SAAO,KAAP;AACD;;;;ACdD,AAGA;AACA;;AACA,AAAe,SAASC,QAAT,CACbC,SADa,EAEbvI,SAFa,EAGJ;AACT,MAAewI,SAAf,GAA0CD,SAA1C,CAAQpP,KAAR;AAAA,MAA6BsP,QAA7B,iCAA0CF,SAA1C;;AACA,MAAeG,SAAf,GAA0C1I,SAA1C,CAAQ7G,KAAR;AAAA,MAA6BwP,QAA7B,iCAA0C3I,SAA1C;;AAEA,SACE,CAACkI,cAAc,CAACM,SAAD,EAAYE,SAAZ,CAAf,IAAyC,CAACR,cAAc,CAACO,QAAD,EAAWE,QAAX,CAD1D;AAGD;;ACXD;AACA;;AACA,AAAe,SAASC,qBAAT,CACb5I,SADa,EAEb6I,SAFa,EAGJ;AACT,SACE,CAACP,QAAQ,CAAC,KAAKrM,KAAN,EAAa+D,SAAb,CAAT,IAAoCkI,cAAc,CAAC,KAAK7L,KAAN,EAAawM,SAAb,CADpD;AAGD;;;;"}