{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "vitest run src/tests/e2e", "test:integration": "vitest run src/tests/integration"}, "dependencies": {"@tanstack/react-query": "^5.80.6", "@tanstack/react-query-devtools": "^5.80.7", "@types/node": "^24.0.0", "@types/react-window": "^1.8.8", "antd": "^5.26.0", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "date-fns": "^4.1.0", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "react-router-dom": "^7.6.2", "react-window": "^1.8.11", "tailwindcss": "^3.4.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}}