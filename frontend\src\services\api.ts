import axios from 'axios';
import type { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import type {
  ApiResponse,
  PaginatedResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  Email,
  Folder,
  Contact,
  ComposeEmailRequest,
  SearchParams
} from '../types';

// 重试配置接口
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition: (error: AxiosError) => boolean;
}

// 默认重试配置
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error: AxiosError) => {
    // 只对网络错误和5xx服务器错误进行重试
    return !error.response || (error.response.status >= 500 && error.response.status < 600);
  },
};

// 延迟函数
const delay = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

class ApiService {
  private api: AxiosInstance;
  private retryConfig: RetryConfig;

  constructor(retryConfig: RetryConfig = DEFAULT_RETRY_CONFIG) {
    this.retryConfig = retryConfig;

    this.api = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器 - 添加认证token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理错误和token刷新
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token过期，尝试刷新
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken) {
            try {
              const response = await this.refreshToken(refreshToken);
              localStorage.setItem('token', response.data.data.token);
              // 重试原请求
              error.config.headers.Authorization = `Bearer ${response.data.data.token}`;
              return this.api.request(error.config);
            } catch (refreshError) {
              // 刷新失败，清除token并跳转到登录页
              console.error(refreshError);
              localStorage.removeItem('token');
              localStorage.removeItem('refreshToken');
              window.location.href = '/login';
            }
          } else {
            // 没有refresh token，直接跳转到登录页
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  // 重试机制
  private async retryRequest<T>(
    requestFn: () => Promise<T>,
    config: RetryConfig = this.retryConfig
  ): Promise<T> {
    let lastError: AxiosError;

    for (let attempt = 0; attempt <= config.retries; attempt++) {
      try {
        return await requestFn();
      } catch (error) {
        lastError = error as AxiosError;

        // 如果不是最后一次尝试且满足重试条件
        if (attempt < config.retries && config.retryCondition(lastError)) {
          // 计算延迟时间（指数退避）
          const delayTime = config.retryDelay * Math.pow(2, attempt);
          await delay(delayTime);
          continue;
        }

        // 不满足重试条件或已达到最大重试次数
        throw error;
      }
    }

    throw lastError!;
  }

  // 错误处理和用户友好的错误消息
  private handleError(error: AxiosError): never {
    // 网络错误
    if (!error.response) {
      throw new Error('网络连接失败，请检查您的网络连接');
    }

    // 根据状态码提供友好的错误消息
    const status = error.response.status;
    const errorData = error.response.data as any;

    switch (status) {
      case 400:
        throw new Error(errorData?.error || '请求参数错误');
      case 401:
        throw new Error(errorData?.error || '身份验证失败，请重新登录');
      case 403:
        throw new Error(errorData?.error || '权限不足，无法执行此操作');
      case 404:
        throw new Error(errorData?.error || '请求的资源不存在');
      case 409:
        throw new Error(errorData?.error || '资源冲突，请检查后重试');
      case 429:
        throw new Error(errorData?.error || '请求过于频繁，请稍后重试');
      case 500:
        throw new Error(errorData?.error || '服务器内部错误，请稍后重试');
      case 502:
        throw new Error('服务暂时不可用，请稍后重试');
      case 503:
        throw new Error('服务维护中，请稍后重试');
      default:
        throw new Error(errorData?.error || `请求失败 (${status})`);
    }
  }

  // 用户相关API
  async updateUser(updates: Partial<User>): Promise<AxiosResponse<ApiResponse<User>>> {
    return this.api.put('/user/profile', updates);
  }

  // 认证相关API
  async login(credentials: LoginRequest): Promise<AxiosResponse<ApiResponse<AuthResponse>>> {
    try {
      return await this.retryRequest(() => this.api.post('/auth/login', credentials));
    } catch (error) {
      this.handleError(error as AxiosError);
    }
  }

  async register(userData: RegisterRequest): Promise<AxiosResponse<ApiResponse<AuthResponse>>> {
    try {
      return await this.api.post('/auth/register', userData);
    } catch (error) {
      this.handleError(error as AxiosError);
    }
  }

  async logout(): Promise<AxiosResponse<ApiResponse<void>>> {
    try {
      return await this.api.post('/auth/logout');
    } catch (error) {
      this.handleError(error as AxiosError);
    }
  }

  async refreshToken(refreshToken: string): Promise<AxiosResponse<ApiResponse<{ token: string }>>> {
    try {
      return await this.retryRequest(() => this.api.post('/auth/refresh', { refreshToken }));
    } catch (error) {
      this.handleError(error as AxiosError);
    }
  }

  async getCurrentUser(): Promise<AxiosResponse<ApiResponse<User>>> {
    try {
      return await this.retryRequest(() => this.api.get('/auth/me'));
    } catch (error) {
      this.handleError(error as AxiosError);
    }
  }

  // 邮件相关API
  async getEmails(params?: {
    folderId?: string;
    page?: number;
    limit?: number;
    search?: SearchParams;
  }): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Email>>>> {
    return this.api.get('/emails', { params });
  }

  async getEmail(id: string): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.get(`/emails/${id}`);
  }

  async sendEmail(emailData: ComposeEmailRequest): Promise<AxiosResponse<ApiResponse<Email>>> {
    const formData = new FormData();
    formData.append('to', JSON.stringify(emailData.to));
    if (emailData.cc) formData.append('cc', JSON.stringify(emailData.cc));
    if (emailData.bcc) formData.append('bcc', JSON.stringify(emailData.bcc));
    formData.append('subject', emailData.subject);
    formData.append('content', emailData.content);
    
    if (emailData.attachments) {
      emailData.attachments.forEach((file) => {
        formData.append(`attachments`, file);
      });
    }

    return this.api.post('/emails', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  async updateEmail(id: string, updates: Partial<Email>): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.put(`/emails/${id}`, updates);
  }

  async deleteEmail(id: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.delete(`/emails/${id}`);
  }

  async markAsRead(id: string): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.put(`/emails/${id}`, { isRead: true });
  }

  async markAsUnread(id: string): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.put(`/emails/${id}`, { isRead: false });
  }

  async starEmail(id: string): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.put(`/emails/${id}`, { isStarred: true });
  }

  async unstarEmail(id: string): Promise<AxiosResponse<ApiResponse<Email>>> {
    return this.api.put(`/emails/${id}`, { isStarred: false });
  }

  // 文件夹相关API
  async getFolders(): Promise<AxiosResponse<ApiResponse<Folder[]>>> {
    return this.api.get('/folders');
  }

  async getFolder(folderId: string): Promise<AxiosResponse<ApiResponse<Folder>>> {
    return this.api.get(`/folders/${folderId}`);
  }

  async getFolderStats(folderId: string): Promise<AxiosResponse<ApiResponse<any>>> {
    return this.api.get(`/folders/${folderId}/stats`);
  }

  async createFolder(folderData: Partial<Folder>): Promise<AxiosResponse<ApiResponse<Folder>>> {
    return this.api.post('/folders', folderData);
  }

  async updateFolder(id: string, updates: Partial<Folder>): Promise<AxiosResponse<ApiResponse<Folder>>> {
    return this.api.put(`/folders/${id}`, updates);
  }

  async deleteFolder(id: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.delete(`/folders/${id}`);
  }

  // 联系人相关API
  async getContacts(): Promise<AxiosResponse<ApiResponse<Contact[]>>> {
    return this.api.get('/contacts');
  }

  async createContact(contactData: Partial<Contact>): Promise<AxiosResponse<ApiResponse<Contact>>> {
    return this.api.post('/contacts', contactData);
  }

  async updateContact(id: string, updates: Partial<Contact>): Promise<AxiosResponse<ApiResponse<Contact>>> {
    return this.api.put(`/contacts/${id}`, updates);
  }

  async deleteContact(id: string): Promise<AxiosResponse<ApiResponse<void>>> {
    return this.api.delete(`/contacts/${id}`);
  }

  // 搜索API
  async searchEmails(params: SearchParams): Promise<AxiosResponse<ApiResponse<PaginatedResponse<Email>>>> {
    return this.api.post('/emails/search', params);
  }

  // 文件上传API
  async uploadAttachment(file: File): Promise<AxiosResponse<ApiResponse<{ url: string; filename: string }>>> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

export const apiService = new ApiService();
export default apiService;
