{"name": "mailparser", "version": "3.7.3", "description": "Parse e-mails", "main": "index.js", "scripts": {"test": "grunt", "update": "rm -rf node_modules package-lock.json && ncu -u && npm install"}, "author": "<PERSON><PERSON>", "contributors": [{"name": "<PERSON>", "email": "petersalo<PERSON><EMAIL>", "url": "https://github.com/petersalomonsen"}], "license": "MIT", "dependencies": {"encoding-japanese": "2.2.0", "he": "1.2.0", "html-to-text": "9.0.5", "iconv-lite": "0.6.3", "libmime": "5.3.6", "linkify-it": "5.0.0", "mailsplit": "5.4.3", "nodemailer": "7.0.3", "punycode.js": "2.3.1", "tlds": "1.259.0"}, "devDependencies": {"ajv": "8.17.1", "eslint": "8.57.0", "eslint-config-nodemailer": "1.2.0", "eslint-config-prettier": "9.1.0", "grunt": "1.6.1", "grunt-cli": "1.5.0", "grunt-contrib-nodeunit": "5.0.0", "grunt-eslint": "24.3.0", "iconv": "3.0.1", "random-message": "1.1.0"}, "repository": {"type": "git", "url": "https://github.com/nodemailer/mailparser.git"}, "bugs": {"url": "https://github.com/nodemailer/mailparser/issues"}}