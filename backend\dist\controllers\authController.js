"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logout = exports.refreshToken = exports.getCurrentUser = exports.login = exports.register = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const memoryStore_1 = require("../database/memoryStore");
const auth_1 = require("../middleware/auth");
const types_1 = require("../types");
const errorHandler_1 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
// 用户注册
exports.register = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, username, displayName, password } = req.body;
    // 验证输入
    if (!email || !username || !displayName || !password) {
        throw new types_1.AppError('所有字段都是必填的', 400);
    }
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        throw new types_1.AppError('邮箱格式无效', 400);
    }
    // 验证密码长度
    if (password.length < 6) {
        throw new types_1.AppError('密码至少需要6位', 400);
    }
    // 检查邮箱是否已存在
    const existingUserByEmail = await memoryStore_1.memoryDB.getUserByEmail(email);
    if (existingUserByEmail) {
        throw new types_1.AppError('该邮箱已被注册', 409);
    }
    // 检查用户名是否已存在
    const existingUserByUsername = await memoryStore_1.memoryDB.getUserByUsername(username);
    if (existingUserByUsername) {
        throw new types_1.AppError('该用户名已被使用', 409);
    }
    // 加密密码
    const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
    const passwordHash = await bcryptjs_1.default.hash(password, saltRounds);
    // 创建用户
    const newUser = await memoryStore_1.memoryDB.createUser({
        email,
        username,
        displayName,
        passwordHash,
        isActive: true,
    });
    // 生成tokens
    const { accessToken, refreshToken } = (0, auth_1.generateTokens)({
        id: newUser.id,
        email: newUser.email,
        username: newUser.username,
    });
    // 返回用户信息（不包含密码）
    const userResponse = {
        id: newUser.id,
        email: newUser.email,
        username: newUser.username,
        displayName: newUser.displayName,
        avatarUrl: newUser.avatarUrl,
        createdAt: newUser.createdAt,
        updatedAt: newUser.updatedAt,
        isActive: newUser.isActive,
    };
    logger_1.logger.info('User registered successfully', { userId: newUser.id, email: newUser.email });
    const response = {
        success: true,
        data: {
            user: userResponse,
            token: accessToken,
            refreshToken,
        },
        message: '注册成功',
    };
    res.status(201).json(response);
});
// 用户登录
exports.login = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    // 验证输入
    if (!email || !password) {
        throw new types_1.AppError('邮箱和密码都是必填的', 400);
    }
    // 查找用户
    const user = await memoryStore_1.memoryDB.getUserByEmail(email);
    if (!user) {
        throw new types_1.AppError('邮箱或密码错误', 401);
    }
    // 检查用户是否激活
    if (!user.isActive) {
        throw new types_1.AppError('账户已被禁用', 401);
    }
    // 验证密码
    const isPasswordValid = await bcryptjs_1.default.compare(password, user.passwordHash);
    if (!isPasswordValid) {
        throw new types_1.AppError('邮箱或密码错误', 401);
    }
    // 生成tokens
    const { accessToken, refreshToken } = (0, auth_1.generateTokens)({
        id: user.id,
        email: user.email,
        username: user.username,
    });
    // 返回用户信息（不包含密码）
    const userResponse = {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.displayName,
        avatarUrl: user.avatarUrl,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        isActive: user.isActive,
    };
    logger_1.logger.info('User logged in successfully', { userId: user.id, email: user.email });
    // 登录成功后，可以启动邮件同步（这里暂时注释掉）
    // imapService.startPolling(user.id);
    const response = {
        success: true,
        data: {
            user: userResponse,
            token: accessToken,
            refreshToken,
        },
        message: '登录成功',
    };
    res.json(response);
});
// 获取当前用户信息
exports.getCurrentUser = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        throw new types_1.AppError('用户未认证', 401);
    }
    // 返回用户信息（不包含密码）
    const userResponse = {
        id: req.user.id,
        email: req.user.email,
        username: req.user.username,
        displayName: req.user.displayName,
        avatarUrl: req.user.avatarUrl,
        createdAt: req.user.createdAt,
        updatedAt: req.user.updatedAt,
        isActive: req.user.isActive,
    };
    const response = {
        success: true,
        data: userResponse,
        message: '获取用户信息成功',
    };
    res.json(response);
});
// 刷新token
exports.refreshToken = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken: token } = req.body;
    if (!token) {
        throw new types_1.AppError('刷新令牌缺失', 400);
    }
    // 这里应该验证refresh token，为简化起见，暂时跳过
    // 在实际应用中，应该将refresh token存储在数据库中并验证
    const response = {
        success: true,
        data: {
            token: 'new-access-token', // 这里应该生成新的access token
        },
        message: '令牌刷新成功',
    };
    res.json(response);
});
// 用户登出
exports.logout = (0, errorHandler_1.asyncHandler)(async (req, res) => {
    // 在实际应用中，这里应该将token加入黑名单或从数据库中删除refresh token
    logger_1.logger.info('User logged out', { userId: req.user?.id });
    const response = {
        success: true,
        data: null,
        message: '登出成功',
    };
    res.json(response);
});
//# sourceMappingURL=authController.js.map