!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(e="undefined"!=typeof globalThis?globalThis:e||self).memoizeOne=n()}(this,(function(){"use strict";var e=Number.isNaN||function(e){return"number"==typeof e&&e!=e};function n(n,t){if(n.length!==t.length)return!1;for(var r=0;r<n.length;r++)if(i=n[r],o=t[r],!(i===o||e(i)&&e(o)))return!1;var i,o;return!0}return function(e,t){var r;void 0===t&&(t=n);var i,o=[],f=!1;return function(){for(var n=[],u=0;u<arguments.length;u++)n[u]=arguments[u];return f&&r===this&&t(n,o)||(i=e.apply(this,n),f=!0,r=this,o=n),i}}}));
