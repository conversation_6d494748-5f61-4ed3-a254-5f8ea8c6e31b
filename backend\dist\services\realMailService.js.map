{"version": 3, "file": "realMailService.js", "sourceRoot": "", "sources": ["../../src/services/realMailService.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,4DAAoC;AACpC,6DAAmC;AACnC,uCAAoC;AACpC,2CAA0C;AAC1C,4CAAyC;AA0EzC,MAAa,eAAe;IAK1B,YAAY,MAAkB;QAC5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC5B,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC9B,IAAI,CAAC;YACH,eAAe;YACf,IAAI,CAAC,eAAe,GAAG,oBAAU,CAAC,eAAe,CAAC;gBAChD,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC3B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;gBAC/B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC3B,IAAI,EAAE,IAAI;gBACV,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,GAAG;aACjB,CAAC,CAAC;YAEH,aAAa;YACb,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YACpC,eAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAE3B,WAAW;YACX,IAAI,CAAC,YAAY,GAAG,MAAM,iBAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACvE,eAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,KAAmB,EAAE,SAAiB;QACpD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG;gBACxD,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC3E,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC5E,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC9E,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,IAAI,EAAE,KAAK,CAAC,WAAW;gBACvB,IAAI,EAAE,KAAK,CAAC,WAAW;gBACvB,WAAW,EAAE,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;oBAC1C,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,WAAW,EAAE,GAAG,CAAC,WAAW;iBAC7B,CAAC,CAAC;gBACH,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,QAAQ;gBACpC,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAEhE,SAAS;YACT,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YAEpE,YAAY;YACZ,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;YAEvD,eAAM,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YAC3C,OAAO,MAAM,CAAC,SAAS,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC/B,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;YACzF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,SAAiB,EAAE,QAAgB,EAAE,UAKjD,EAAE;QAKJ,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;YAC7D,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;YAElC,IAAI,KAAK,GAAG;;;;;;;;;;;;;;;OAeX,CAAC;YAEF,MAAM,MAAM,GAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAE5C,IAAI,UAAU,EAAE,CAAC;gBACf,KAAK,IAAI,qBAAqB,CAAC;YACjC,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,KAAK,IAAI,wEAAwE,CAAC;gBAClF,MAAM,aAAa,GAAG,IAAI,MAAM,GAAG,CAAC;gBACpC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAC3D,CAAC;YAED,KAAK,IAAI,iDAAiD,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAE3B,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAI,IAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;YAEnE,OAAO;YACP,IAAI,UAAU,GAAG;;;;;;;OAOhB,CAAC;YAEF,MAAM,WAAW,GAAU,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAEjD,IAAI,UAAU,EAAE,CAAC;gBACf,UAAU,IAAI,qBAAqB,CAAC;YACtC,CAAC;YAED,IAAI,MAAM,EAAE,CAAC;gBACX,UAAU,IAAI,wEAAwE,CAAC;gBACvF,MAAM,aAAa,GAAG,IAAI,MAAM,GAAG,CAAC;gBACpC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;YAChE,CAAC;YAED,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAC7E,MAAM,KAAK,GAAI,SAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;YAE5C,OAAO;gBACL,MAAM;gBACN,KAAK;gBACL,OAAO,EAAE,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;aACxC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,SAAiB,EAAE,OAAe;QAC/C,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;;;;;;;;;;;;OAeb,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;YAE5E,IAAK,IAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAE,IAAc,CAAC,CAAC,CAAC,CAAC,CAAC;YAErD,OAAO;YACP,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,KAAK,CAAC,WAAW,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC;YAC9D,CAAC;YAED,QAAQ;YACR,IAAI,CAAE,IAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;gBAChC,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACjD,CAAC;YAED,OAAO,KAAK,CAAC;QAEf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;YACjC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAChC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;;;;;OAQb,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;YAEnE,OAAQ,IAAc,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACjC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;gBACrB,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,WAAW,EAAE,GAAG,CAAC,YAAY;gBAC7B,IAAI,EAAE,GAAG,CAAC,IAAI;gBACd,UAAU,EAAE,GAAG,CAAC,WAAW;gBAC3B,WAAW,EAAE,GAAG,CAAC,YAAY;aAC9B,CAAC,CAAC,CAAC;QAEN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,SAAiB;QACpC,IAAI,MAAM,GAAoB,IAAI,CAAC;QAEnC,IAAI,CAAC;YACH,aAAa;YACb,MAAM,GAAG,IAAI,mBAAQ,CAAC;gBACpB,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC3B,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC3B,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;gBAC/B,IAAI,EAAE;oBACJ,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,oBAAoB;iBACvD;gBACD,MAAM,EAAE,KAAK;aACd,CAAC,CAAC;YAEH,MAAM,MAAM,CAAC,OAAO,EAAE,CAAC;YAEvB,SAAS;YACT,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YAEtC,KAAK,MAAM,OAAO,IAAI,SAAS,EAAE,CAAC;gBAChC,IAAI,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACzC,SAAS;gBACX,CAAC;gBAED,OAAO;gBACP,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAEvD,IAAI,CAAC;oBACH,OAAO;oBACP,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE;wBACnC,QAAQ,EAAE,IAAI;wBACd,aAAa,EAAE,IAAI;wBACnB,MAAM,EAAE,IAAI;qBACb,CAAC,CAAC;oBAEH,IAAI,KAAK,EAAE,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;wBACrC,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;oBAClE,CAAC;gBAEH,CAAC;wBAAS,CAAC;oBACT,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,CAAC;YACH,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,cAAc,SAAS,EAAE,CAAC,CAAC;QAEzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;gBAAS,CAAC;YACT,IAAI,MAAM,EAAE,CAAC;gBACX,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC;YACxB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,OAAY,EAAE,UAAkB;QAClF,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAA,yBAAY,EAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YAElD,MAAM,KAAK,GAAiB;gBAC1B,SAAS,EAAE,MAAM,CAAC,SAAS,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE;gBAC/D,QAAQ,EAAE,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS;gBAC9C,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,OAAO;gBAClC,IAAI,EAAE;oBACJ,OAAO,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE;oBAC7C,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI;iBAClC;gBACD,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACvC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC,IAAI,EAAE;gBACT,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACvC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;gBACH,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,CAAC;oBACzC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;oBAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;iBAChB,CAAC,CAAC;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO;gBAC1C,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;gBAC/B,WAAW,EAAE,MAAM,CAAC,IAAI;gBACxB,WAAW,EAAE,MAAM,CAAC,IAAI;gBACxB,WAAW,EAAE,MAAM,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC;oBAClD,QAAQ,EAAE,GAAG,CAAC,QAAQ,IAAI,YAAY;oBACtC,WAAW,EAAE,GAAG,CAAC,WAAW,IAAI,0BAA0B;oBAC1D,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;oBACnB,OAAO,EAAE,GAAG,CAAC,OAAO;iBACrB,CAAC,CAAC;gBACH,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,OAAO,CAAC;aACtD,CAAC;YAEF,WAAW;YACX,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAE/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,GAAQ;QAC5B,OAAO;YACL,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;YACrB,SAAS,EAAE,GAAG,CAAC,UAAU;YACzB,QAAQ,EAAE,GAAG,CAAC,SAAS;YACvB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,IAAI,EAAE;gBACJ,OAAO,EAAE,GAAG,CAAC,YAAY;gBACzB,IAAI,EAAE,GAAG,CAAC,SAAS;aACpB;YACD,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,CAAC;YACxC,EAAE,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,CAAC;YACxC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,IAAI,IAAI,CAAC;YAC1C,OAAO,EAAE,GAAG,CAAC,QAAQ;YACrB,IAAI,EAAE,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,aAAa;YACxC,WAAW,EAAE,GAAG,CAAC,SAAS;YAC1B,WAAW,EAAE,GAAG,CAAC,SAAS;YAC1B,WAAW,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,SAAS;YAC5D,QAAQ,EAAE,GAAG,CAAC,QAAQ;SACvB,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,OAAe;QAC/C,MAAM,KAAK,GAAG;;;;KAIb,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;QACjE,OAAO,IAAa,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,SAAiB,EAAE,OAAe;QAC9D,MAAM,KAAK,GAAG;;;;;;KAMb,CAAC;QAEF,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC,CAAC;IAC/D,CAAC;IAEO,KAAK,CAAC,YAAY,CACxB,SAAiB,EACjB,KAAmB,EACnB,MAAc,EACd,SAAkB,EAClB,YAAqB;QAErB,MAAM,KAAK,GAAG;;;;KAIb,CAAC;QAEF,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,EAAE,CAAC;QAC7C,MAAM,MAAM,GAAG,MAAM,KAAK,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;QAErD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE;YACrC,KAAK,CAAC,IAAI,CAAC,OAAO;YAClB,SAAS;YACT,KAAK,CAAC,OAAO;YACb,MAAM;YACN,YAAY;YACZ,MAAM;YACN,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAmB,EAAE,SAAiB,EAAE,UAAkB;QACxF,kBAAkB;QAClB,eAAe;IACjB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAmB,EAAE,SAAiB,EAAE,UAAkB;QAC1F,gBAAgB;QAChB,eAAe;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAAY;QACzC,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtE,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,CAAC,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;YAC5C,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG;gBAAE,OAAO,MAAM,CAAC;YACnD,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG;gBAAE,OAAO,KAAK,CAAC;QACnD,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;gBACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;YAC/B,CAAC;YACD,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;YAChC,CAAC;YACD,eAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;CACF;AApdD,0CAodC"}